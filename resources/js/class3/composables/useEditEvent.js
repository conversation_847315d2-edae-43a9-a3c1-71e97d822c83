import { computed, ref } from 'vue';
import usePlanning from './usePlanning.js';
import useLang from './useLang.js';
import useDateTime from "./useDateTime.js";

const { events, studentGroup } = usePlanning();
const { translate } = useLang();
const { getDateOfWeekByDayName }  = useDateTime();

const eventToEdit = ref(null);
const doRevert = ref(() => {});

export default function useEditEvents () {
    const setEventToEdit = (obj) => {
        eventToEdit.value = obj;
    };

    /**
     * this will contain the function to be called when
     * the user clicks "cancel". Provided by the fullcalendar package
     * currently not in use, this is for drag/drop of events on the calendar
     */
    const setDoRevert = (func) => {
        doRevert.value = func;
    };

    /**
     * if this is a group lesson we want to show the user all names of students in the group
     * and link to their student card, so we need to return an array of objects {student id, student name}
     */
    const studentNames = computed(() => {
        return eventToEdit.value && eventToEdit.value
            ? eventToEdit.value.students.map(student => {
                return {
                    id: student.id,
                    name: student.name
                };
            })
            : '';
    });

    /**
     * convert a tutoring event into a calendar event
     * to be able to edit this event and reuse the same form
     * we need to make sure the data model is the same
     * @deprecated
     * @param tutoringEvent
     */
    const tutoringEventToCalendarEvent = (tutoringEvent) => {
        const isGroup = tutoringEvent.student.firstname === '-' && tutoringEvent.student.date_of_birth === '1800-01-01';
        const genericData = {
            id: tutoringEvent.id,
            eventId: tutoringEvent.id,
            isSolitary: !tutoringEvent?.isPartOfSeries, // single event or part of series?
            start: tutoringEvent.datetime,
            end: calculateEndtime(tutoringEvent.datetime, tutoringEvent.timespan),
            allDay: false, // no date exceptions, so it will never be an allDay event
            eventType: 'appointment', // no date exceptions, always 'appointment'
            courseName: tutoringEvent.course.name,
            courseId: tutoringEvent.course.id,
            timespan: tutoringEvent.timespan,
            timetable_id: tutoringEvent.timetable_id,
            tutor: tutoringEvent.tutor.name,
            tutorId: tutoringEvent.tutor.id,
            location: tutoringEvent.location.name,
            locationId: tutoringEvent.location.id,
            backgroundColor: tutoringEvent.tutor.hexcolor,
            textColor: 'white',
            remarks: '',
            attendanceNotes: [], // todo: add attendance notes?
            student: tutoringEvent.student,
            studentId: tutoringEvent.student.id,
            studentName: tutoringEvent.student.name,
            studentLastName: tutoringEvent.student.lastname,
            hasIncompleteChecklist: false,
            tutorColor: tutoringEvent.tutor.hexcolor,
            split: 3,
            class: 'location-' + tutoringEvent.location.id,
            content: ''
        };
        const individualData = {
            ...genericData,
            title: tutoringEvent.course.name + translate('generic.with') + tutoringEvent.student.name,
            isAStudentGroup: false,
            nrOfStudents: 1,
            students: [tutoringEvent.student]
        };
        const groupData = {
            ...genericData,
            title: tutoringEvent.course.name + ' with ' + studentGroup.name,
            isAStudentGroup: true,
            studentName: tutoringEvent.student.lastname,
            students: studentGroup.value?.students,
            nrOfStudents: studentGroup.value?.students.length
        };
        eventToEdit.value = isGroup ? groupData : individualData;
    };

    /**
     * calculate the endtime of an event
     * @param start
     * @param timespan e.g. 30 minutes
     * @returns {string}
     */
    const calculateEndtime = (start, timespan) => {
        const end = new Date(start);
        // convert timespan string (30 minutes) to integer minutes (30)
        timespan = parseInt(timespan);
        end.setMinutes(end.getMinutes() + timespan);
        // return format yyyy-mm-dd hh:mm:ss (e.g. 2021-10-31 12:00:00) and ignore timezone
        const yyyy = end.getFullYear();
        const mm = String(end.getMonth() + 1).padStart(2, '0'); // Months are zero-based
        const dd = String(end.getDate()).padStart(2, '0');
        const HH = String(end.getHours()).padStart(2, '0');
        const MM = String(end.getMinutes()).padStart(2, '0');
        const ss = String(end.getSeconds()).padStart(2, '0');
        return `${yyyy}-${mm}-${dd} ${HH}:${MM}:${ss}`;
    };

    /**
     * Create an extra event based on the week number and year.
     *
     * @param {number} weekNumber - The week number.
     * @param {number} year - The year.
     */
    const createExtraEvent = (weekNumber, year) => {
        // create a clone of the first event, not a copy by reference
        const eventCopy = { ...events.value[0] };
        // Get the correct date for the event in the target week.
        const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        // Determine the correct date for the event in the target week (so the same day-of-week and the same time).
        // The time stays the same.
        const originatingDay = new Date(eventCopy.start).getDay();
        const targetDate = getDateOfWeekByDayName(weekNumber, parseInt(year), weekdays[originatingDay]);

        // Extract time in HH:MM:SS format to ensure consistent formatting
        const startDateTime = new Date(eventCopy.start);
        const endDateTime = new Date(eventCopy.end);
        const startTime = startDateTime.toTimeString().substring(0, 8); // HH:MM:SS
        const endTime = endDateTime.toTimeString().substring(0, 8); // HH:MM:SS

        eventCopy.start = targetDate + ' ' + startTime;
        eventCopy.end = targetDate + ' ' + endTime;
        eventCopy.id = 999999;
        eventCopy.eventId = 999999;
        events.value.push(eventCopy);
    }

    return {
        createExtraEvent,
        doRevert,
        eventToEdit,
        setDoRevert,
        setEventToEdit,
        studentNames,
        //tutoringEventToCalendarEvent
    };
}
