<template>
    <div class="row mb-2">
        <div class="col-md-2">
            <a :href="studentLink">
                {{ studentName }}
            </a>
        </div>
        <div class="col-md-2">{{ timetable.course_name }}</div>
        <div class="col-md-3">{{ timetable.recurrence }}</div>
        <div class="col-md-5" v-if="timetable.event_summary">
            <span class="badge bg-info text-dark me-1">
                {{ ucFirst(translate('generic.billable')) }}:
                <span class="fix-number-width">{{ numberOfBillableEvents }}</span>
            </span>
            <span class="badge bg-primary me-1">
                {{ ucFirst(translateChoice('generic.totals', 1)) }}:
                <span class="fix-number-width">{{ timetable.event_summary['total-events'] }}</span>
            </span>
            <span class="badge bg-success me-1">
                {{ ucFirst(translateChoice('generic.futureappointments', timetable.event_summary.future)) }}:
                <span class="fix-number-width">{{ timetable.event_summary.future }}</span>
            </span>
            <span class="badge bg-secondary me-1">
                {{ ucFirst(translate('generic.past')) }}:
                <span class="fix-number-width">{{ timetable.event_summary.past }}</span>
            </span>
            <span v-if="timetable.event_summary.blocked > 0" class="badge bg-warning text-dark">
                {{ ucFirst(translate('generic.blocked')) }}:
                <span class="fix-number-width">{{ timetable.event_summary.blocked }}</span>
            </span>
        </div>
        <div class="col-md-5" v-else>
            <!-- should never happen -->
            <span class="badge badge-primary mr-1">
                {{ ucFirst(translateChoice('generic.totals', 1)) }}:
                <span class="fix-number-width">{{ timetable.event_count }} }}</span>
            </span>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import useLang from "../../composables/useLang";

const { ucFirst, translate, translateChoice } = useLang();

const props = defineProps({
    timetable: {
        type: Object,
        required: true
    },
    isGroup: {
        type: Boolean,
        default: false
    }
});

// Compute the student name based on whether it's a group or individual
const studentName = computed(() => {
    return props.isGroup
        ? props.timetable.student_last_name
        : props.timetable.student_name;
});

// Compute the link based on whether it's a group or individual
const studentLink = computed(() => {
    return props.isGroup
        ? `/timetableedit/${props.timetable.registration_id}`
        : `/students/${props.timetable.student_id}/edit`;
});

const numberOfBillableEvents = computed(() => {
    return props.timetable.event_summary['total-events'] - props.timetable.event_summary.blocked;
});
</script>

<style scoped>
.fix-number-width {
    display: inline-block;
    min-width: 2rem;
}
</style>
