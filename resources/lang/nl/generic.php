<?php
return [
    "a" => "een",
    "absentnotificationtoolate" => "absent, afmelding te laat",
    "absentwithnotification" => "absent met afmelden",
    "absentwithoutnotification" => "absent zonder afmelden",
    "access" => "toegang",
    "accesscodes" => "toegangscode|toegangscodes",
    "accessgrantedallstudents" => "toegang verleend aan :affected leerling.|toegang verleend aan :affected leerlingen.",
    "accessgrantedforstudent" => "toegang verleend aan :studentname",
    "accessnotallowed" => "toegang niet toegestaan",
    "accessrevokedallstudents" => "toegang ingetrokken voor :affected leerling.|toegang ingetrokken voor :affected leerlingen.",
    "accessrevokedforstudent" => "toegang ingetrokken voor :studentname",
    "accesstoken" => "toegangscode",
    "accesstokeninvalid" => "toegangscode niet geldig",
    "actionaftersinglelesson" => "actie na losse les",
    "actionaftersinglelessonforstudent" => "actie na losse les voor leerling :student|actie na losse les voor leerlingen :student",
    "actionnaftertask" => ":student open taak|:student open taken",
    "actionnaftertaskpastdue" => ":student verlopen taak|:student verlopen taken",
    "actionnaftertriallesson" => "actie na proefles",
    "actionnaftertriallessonforstudent" => "actie na proefles voor leerling :student|actie na proefles voor leerlingen :student",
    "actionneeded" => "actie nodig",
    "actions" => "actie|acties",
    "actiontutor" => "actie docent",
    "activeschoolyearavailable" => "actief schooljaar aanwezig",
    "activetutors" => "huidige docenten",
    "add" => "toevoegen",
    "addaccesstokens" => "cre&euml;er toegangscode",
    "addacourse" => "voeg een cursus toe",
    "addallstudents" => "voeg alle leerlingen toe",
    "addallstudentswithactivecourse" => "voeg alle leerlingen met actieve cursusinschrijving toe",
    "adddeteexception" => "voeg datumuitzondering toe",
    "addedsuccessfully" => "toegevoegd",
    "addedtaskfor" => "taak toegevoegd voor",
    "addextraevent" => "Voeg een extra les toe",
    "additionallycreatetask" => "gelijk taak aanmaken voor",
    "addlibrary" => "bibliotheek toevoegen",
    "addlink" => "link toevoegen",
    "addlocation" => "cursuslocatie toevoegen",
    "addnewfile" => "bestand toevoegen",
    "addnewlink" => "weblink toevoegen",
    "addrecurrenceoption" => "herhaaloptie toevoegen",
    "address" => "adres",
    "addressline1" => "adresregel 1",
    "addressline2" => "adresregel 2",
    "addschoolyear" => "schooljaar toevoegen",
    "addstudenttogroup" => "leerling aan deze groep toevoegen",
    "addtag" => "voeg tag toe",
    "addtask" => "taak toevoegen",
    "addtimeslice" => "tijdsdeel toevoegen",
    "addtoschedule" => "voeg toe aan schema",
    "addtostudentgroup" => "voeg toe aan leerlinggroep",
    "addtriallessonrequest" => "aanvraag proefles toevoegen",
    "addtutor" => "docent toevoegen",
    "adminactionneeded" => "admin actie nodig",
    "adolescents" => "jongere|jongeren",
    "adult" => "volwassen",
    "adults" => "volwassene|volwassenen",
    "adultthreshold" => "leeftijd volwassen",
    "afterthatyoucanschedule" => "vervolgens kan de afspraak worden ingepland, net als andere afspraken.",
    "age" => "leeftijd",
    "agegroup" => "leeftijdsgroep",
    "alert" => "alarm",
    "alerts" => "waarschuwing|waarschuwingen",
    "all" => "allemaal",
    "allappointments" => "alle afspraken",
    "allday" => "hele dag",
    "allentries" => "volledig e-mail log",
    "allevents" => "alle afspraken",
    "alllocationsnofilter" => "alle locaties (geen filter)",
    "allowedipaddressesbroadcast" => "toegestane IP-adressen voor info bord",
    "allreadyyoumayclosethiswindow" => "Alles is opgeslagen. Als je beschikbaarheid zo compleet is kun je deze pagina sluiten",
    "allregistrations" => "alle inschrijvingen",
    "allseriesareselected" => "<span class=\"text-danger\">Alle afspraakreeksen zijn gesecteerd.</span> Klik op de knop hiernaast om dit aantal in te perken",
    "allstudentsareselected" => "<span class=\"text-danger\">Alle leerlingen zijn gesecteerd.</span> Klik op de knop hiernaast om het aantal doel-leerlingen in te perken",
    "allstudentsingroup" => "alle leerlingen in de groep",
    "alltutorsnofilter" => "alle docenten (geen filter)",
    "alreadysigned" => "deze inschrijving is al ondertekend",
    "analyse" => "analyseer",
    "analyseworklistsforplanning" => "analyseer werklijsten voor planning",
    "analysing" => "analyseren",
    "analysis" => "analyse",
    "analysisfailed" => "analyse mislukt",
    "and" => "en",
    "andendsafter" => "en eindigt na",
    "apply" => "is van toepassing",
    "applyto" => "toepassen op",
    "appointmentchanged" => "afspraak gewijzigd",
    "appointmentofhaschanged" => "de afspraak met :studentName op :datetime is gewijzigd",
    "appointments" => "afspraak|afspraken",
    "appointmentsdeleted" => "afspraak/afspraken verwijderd",
    "appointmentsdeletefailed" => "afspraak/afspraken verwijderen mislukt",
    "appointmentseries" => "afspraakseries",
    "appointmentwillbesentto" => "afspraak wordt verstuurd naar",
    "archive" => "archiveer",
    "archivecourse" => "archiveer cursus",
    "archivedcourses" => "gearchiveerde cursussen",
    "areyousure" => "weet je het zeker",
    "areyousuredeleteallstudents" => "weet je zeker dat je alle leerlingen van deze lijst wilt verwijderen?",
    "areyousuredeleteevent" => "weet je zeker dat je deze afspraak/afspraken wilt verwijderen uit het rooster?",
    "areyousuredeletestudent" => "weet je zeker dat je de gegevens van deze leerling uit de CLASS database wilt verwijderen?",
    "areyousuredeletestudentfromgroup" => "weet je zeker dat je deze leerling uit de leerlinggroep wilt verwijderen?",
    "areyousuredeletestudentgroup" => "weet je zeker dat je de gegevens van deze leerlinggroep uit de CLASS database wilt verwijderen?",
    "areyousuredeletestudentlist" => "weet je zeker dat je deze leerlinglijst wilt verwijderen?",
    "areyousuresendemail" => "weet je zeker dat je deze e-mail wilt versturen?",
    "assignedto" => "toegewezen aan",
    "at" => "om",
    "attachingdocumentfailed" => "koppelen document is mislukt",
    "attachment" => "bijlage",
    "attendance" => "presentie",
    "attendanceoptions" => "presentieoptie|presentieopties",
    "atutor" => "een docent",
    "autoadd" => "automatisch toevoegen",
    "autoapply" => "Automatisch toepassen",
    "availability" => "beschikbaarheid",
    "available" => "beschikbaar",
    "availablecoursegroups" => "beschikbare cursusgroupen",
    "availablefrom" => "beschikbaar vanaf",
    "availableschoolyears" => "beschikbare schooljaren",
    "availableto" => "beschikbaar tot",
    "back" => "terug",
    "bankaccountname" => "bankaccount naam",
    "bankaccountnumber" => "bankaccount nummer",
    "bankdata" => "Bank gegevens",
    "basicdata" => "Basis gegevens",
    "beatleast8characterslong" => "tenminste 8 characters lang",
    "belongstoseriesofappointments" => "hoort bij een serie afspraken",
    "beyondenddateofregistration" => "De volgende datum is groter dan de einddatum van de inschrijving",
    "beyondenddateofschoolyear" => "De volgende datum is groter dan de einddatum van het schooljaar",
    "billable" => "factureerbaar",
    "birthdate" => "geboortedatum",
    "birthdays" => "verjaardagen",
    "blocked" => "geblokkeerd",
    "booklocation" => "reserveer ruimte/locatie",
    "broadcast" => "hele school",
    "by" => "van",
    "calculated" => "berekend",
    "calendar" => "kalender",
    "calendarconflict" => "kalenderconflict",
    "calendarconflicttutororlocation" => "kalenderconflict docent of locatie",
    "cancel" => "annuleren",
    "cannotbedeleted" => "kan niet verwijderd worden",
    "cantdelete" => "De optie wordt gebruikt en kan daarom niet worden verwijderd",
    "cantdeletecourseifstudentsaresubscribed" => "kan de cursus niet verwijderen omdat er studenten aan de groep gekoppeld zijn",
    "canvasnotsupported" => "Je browser ondersteunt geen canvas! Probeer een andere browser.",
    "change" => "wijzig",
    "changealldatesinseries" => "wijzig alle datums in de serie",
    "changeappointments" => "wijzig afspraken",
    "changeevent" => "wijzig gebeurtenis",
    "changepassword" => "wijzig je wachtwoord",
    "changessaved" => "wijzigingen opgeslagen",
    "changeswilleffectthefollowingcourses" => "wijzigingen zullen de volgende cursus beïnvloeden|wijzigingen zullen de volgende cursussen beïnvloeden",
    "changethisdateexception" => "wijzig deze datumuitzondering",
    "checking" => "controleren",
    "checkingplaningstatus" => "status van de planning wordt gecontroleerd",
    "checklist" => "checklijst",
    "checklistcompleted" => "checklijst volledig afgevinkt",
    "checklistdata" => "checklijst gegevens",
    "checklistdeleted" => "checklijst verwijderd",
    "checklistforthisregistration" => "checklijst voor deze inschrijving",
    "checklistincomplete" => "checklijst niet volledig afgevinkt",
    "checklists" => "checklijsten",
    "children" => "kind|kinderen",
    "chooseachecklist" => "kies een checklijst",
    "chooseacourse" => "kies een cursus",
    "choosealocation" => "kies een locatie",
    "chooseastudent" => "kies een leerling",
    "chooseatutor" => "kies een docent",
    "choosecoursegroup" => "kies een cursusgroep",
    "chooseexistingfile" => "kies een eerder geupload bestand (hergebruik)",
    "chooseexistinglink" => "kies een eerder geregistreerde weblink (hergebruik)",
    "choosestudentgroup" => "kies een leerlinggroep",
    "choosestudentorgroup" => "kies een leerling of -groep",
    "choosestudents" => "kies leerling|kies leerlingen",
    "choosestudenttoaddtogroup" => "kies een leerling om aan de groep toe te voegen",
    "choosetriallesson" => "kies een proefles",
    "choosetutor" => "kies docenten",
    "chosen" => "gekozen",
    "chosentimeinvalidchar" => ":field tijd bevat een ongeldig teken",
    "city" => "plaatsnaam",
    "classeaccess" => "toegang ClassE",
    "classurl" => "class url",
    "CLASSwasabletoschedule" => "CLASS kan :nrofappointments afspraken inplannen",
    "classyaccess" => "toegang ClassY",
    "clickhere" => "klik hier",
    "clicktochoosestudentstocreategroup" => "klik alle leerlingen aan om een nieuwe groep mee te maken",
    "clicktodelete" => "klik vuilnisbakje om de koppeling te verwijderen",
    "clicktoedit" => "klik hier om te wijzigen",
    "clicktoopencourseeditpage" => "klik hier om de wijzigpagina van deze cursus te openen",
    "clicktorevealpin" => "klik om de pincode te tonen",
    "close" => "sluiten",
    "closed" => "gesloten",
    "closedtasks" => "afgesloten taken",
    "closedtrialrequests" => "afgesloten proeflesverzoeken",
    "closethistask" => "deze taak afsluiten",
    "closingdate" => "sluitdatum",
    "color" => "kleur",
    "concerns" => "betreft",
    "concernswholeschool" => "betreft de hele school",
    "confirm" => "bevestig",
    "confirmdeletefromstudentlist" => "Deze leerling van de lijst verwijderen?",
    "confirmed" => "bevestigd",
    "confirmignorecurrentstudentschedule" => "<strong>De huidige planning voor deze leerling negeren?</strong><br/><br/>Deze groep heeft een geplande dag en tijd. Door deze leerling uit de groep te halen moeten we de leerling op een andere dag en/of tijd gaan inplannen, de eventuele huidige dag en tijd wordt genegeerd",
    "confirmmyregistration" => "bevestig mijn inschrijving",
    "confirmregistration" => "bevestig je inschrijving",
    "confirmresetpassword" => "bevestig reset wachtwoord",
    "confirmsavechangeevent" => "bevestig opslaan gewijzigde afspraak",
    "confirmshare" => "(optioneel) Ik geef toestemming om media waarop de hierboven geregistreerde leerling te zien of te horen is, te gebruiken voor social media kanalen ter promotie van :school.",
    "conflictDEDates" => "conflict datum",
    "conflictDEDescription" => "conflict reden",
    "conflictDescription" => "conflict omschrijving",
    "conflicts" => "conflict|conflicten",
    "conflictscalendar" => "conflicten: Calendarafspraken",
    "conflictsstudentsnoemail" => "conflicten: Leerlingen zonder e-mail",
    "conflictsstudentsnoregistration" => "conflicten: Leerlingen zonder registratie",
    "conflictstutorsandlocations" => "conflicten: Docenten en locaties",
    "conformplanning" => "conform planning",
    "connectionerror" => "verbindingsfout bij opvragen informatie",
    "contactdata" => "contact gegevens",
    "containatleas1lcletter" => "tenminste 1 kleine letter bevatten",
    "containatleas1specornr" => "tenminste 1 speciaal teken en/of 1 nummer bevatten",
    "containatleas1ucletter" => "tenminste 1 hoofdletter bevatten",
    "contents" => "inhoud",
    "continueallstudentsignore" => "ga verder met alle leerlingen (negeer waarschuwing)",
    "continuedfrompreviousblock" => "voortzetting van het vorige tijdblok",
    "continueonlyvalidstudents" => "ga verder met alleen de leerlingen die recent ingevuld hebben",
    "continuesinnextblock" => "gaat in het volgende tijdblok verder",
    "continuous" => "doorlopend",
    "copy" => "kopieer",
    "copyaddressdata" => "kopieer adresgegevens",
    "copybankdata" => "kopieer bankgegevens",
    "copycontactdata" => "kopieer contactgegevens",
    "copydatafromotherstudent" => "kopieer gegevens van andere leerling",
    "copystudentlists" => "kopieer leerlinglijsten",
    "couldnotcreatecourseregistration" => "kon geen cursus inschrijving registreren omdat deze informatie niet in de aanvraag aanwezig was.",
    "couldnotgettemplatevariables" => "kon de templatevarabelen niet ophalen",
    "couldnotresetprice" => "kon prijs niet herstellen",
    "couldnotresettaxrate" => "kon BTW tarief niet herstellen",
    "couldnotsavedob" => "kon geboortedatum niet opslaan. De aanvrager voerde :dob in.",
    "couldnotsavenewprice" => "kon nieuwe prijs niet opslaan",
    "couldnotsavenewtaxrate" => "kon nieuw BTW tarief niet opslaan",
    "couldntdeterminenextdatetotry" => "kon niet bepalen op welke datum opnieuw te proberen (er is geen herhaalinterval)",
    "couldntfindstartingdatein10" => "kon geen startdatum vinden in de eerst volgende 10 mogelijke datums",
    "couldntreplacevariableintemplate" => "kon templatevariabele :theVariable niet vervangen",
    "count" => "tel",
    "countimagesuploading" => ":countImages afbeeldingen worden geupload",
    "country" => "land",
    "counttargetemailaddresses" => "aantal doel e-mailadressen",
    "couplechecklist" => "koppel checklijst",
    "couplecourse" => "koppel cursus",
    "coupledcourses" => "gekoppelde cursus|gekoppelde cursussen",
    "course" => "cursus",
    "courseadded" => "cursus toegevoegd",
    "coursearchived" => "cursus gearchiveerd",
    "coursedata" => "cursus gegevens",
    "coursefieldscannotbechanged" => "sommige velden kunnen niet gewijzigd worden omdat leerlingen zijn of waren ingeschreven op deze cursus",
    "courseforyouthbuttooold" => "de cursus \":coursename\" is bedoeld voor jongeren maar de leerling is :age.",
    "coursegroup" => "cursusgroep",
    "coursegroupdata" => "cursusgroep gegevens",
    "coursegroupdeleted" => "cursusgroep verwijderd",
    "coursegroups" => "cursusgroepen",
    "coursegroupsmissing" => "geen cursusgroepen gevonden",
    "coursehasbeenadded" => "cursus gekoppeld aan leerlinggroep",
    "coursehasbeenremoved" => "cursus losgekoppeld van de leerlinggroep",
    "coursehascompletedchecklist" => "deze inschrijving heeft een of meer checklijsten die allemaal volledig afgevinkt zijn",
    "coursehasnochecklist" => "deze inschrijving heeft geen gekoppelde checklijst",
    "coursehasnotbeenscheduled" => "cursus is niet ingeroosterd",
    "coursehasuncompletechecklist" => "deze inschrijving heeft een checklijst die nog niet volledig afgevinkt is",
    "courseisscheduledon" => "deze cursus is ingeroosterd op",
    "coursename" => "cursusnaam",
    "coursenotarchived" => "cursus niet gearchiveerd",
    "courseregistration" => "cursusregistratie",
    "courseregistrationendsafter" => "einddatum inschrijving",
    "courseregistrationfor" => "cursusregistratie voor",
    "courseRelationAdded" => "opgeslagen, cursusrelatie toegevoegd",
    "courserelationremoved" => "opgeslagen, cursusrelatie verwijderd",
    "courserelationships" => "cursusrelatie|cursusrelaties",
    "courseremoved" => "cursus losgekoppeld",
    "courses" => "cursus|cursussen",
    "courseslist" => "cursus lijst",
    "coursestobescheduled" => "cursussen in te roosteren voor schooljaar :schoolyear.<br/><small>Nog :nrOfCourses cursus(sen) te doen</small>",
    "coursetaxrateadults" => "cursus btw (volwassenen)",
    "coursetype" => "cursustype",
    "create" => "maak",
    "created" => "aangemaakt",
    "createdat" => "aangemaakt op",
    "createfirstcourse" => "maak je eerste cursus",
    "creategroup" => "maak een groep",
    "createnewappointment" => "nieuwe afspraak inplannen",
    "createnewappointments" => "nieuwe afspraken inplannen",
    "createnewmandatenumber" => "genereer een nieuw mandaatnummer",
    "createnewstudentgroup" => "nieuwe leerlinggroep maken",
    "createnewtoken" => "nieuwe toegangscode maken",
    "createpassword" => "maak wachtwoord",
    "createstudentwiththesevalues" => "nieuwe leerling met deze waardes",
    "createtoken" => "maak token",
    "createworklistsforplanning" => "maak werklijsten voor planning",
    "creatstudentfromtrialrequest" => "maak leerling van proefles verzoek",
    "current" => "huidige",
    "currentcourses" => "huidige cursussen",
    "currentevents" => "op dit moment ingeroosterd",
    "currentlessontime" => "huidige lestijd",
    "currentmessage" => "huidig bericht",
    "currentpassword" => "huidig wachtwoord",
    "currentpasswordisrequired" => "...vul het huidige wachtwoord in",
    "currentregistrations" => "alle huidige inschrijvingen",
    "currentstudentgroups" => "huidige leerlinggroepen voor deze cursus",
    "currentstudents" => "huidige leerlingen",
    "dashboard" => "dashboard",
    "dashboardoverview" => "dashboard overzicht",
    "datasaved" => "gegevens opgeslagen",
    "date" => "datum",
    "dateend" => "einddatum",
    "dateexceptiondata" => "datumuitzondering informatie",
    "dateexceptions" => "datumuitzondering|datumuitzonderingen",
    "dateofbirthmissing" => "geboortedatum mist, verzonnen waarde ingevuld!",
    "datestart" => "startdatum",
    "datetime" => "datum / tijd",
    "datetime_end" => "tot",
    "datetime_start" => "vanaf",
    "datetimecreated" => "datum / tijd gemaakt",
    "datetimeupdated" => "datum / tijd bijgewerkt",
    "dayname" => "dagnaam",
    "days" => "dag|dagen",
    "dayschedule" => "dagschema",
    "daytime" => "dag / tijd",
    "dear" => "beste",
    "defaultchecklists" => "Standaard checklist|Standaard checklists",
    "defaultpassword" => "default password",
    "defaultrecoption" => "",
    "defaultrecoption1" => "1.5 uur per twee weken tot uitschrijven (doorlopend)",
    "defaultrecoption2" => "1.5 uur per week tot uitschrijven (doorlopend)",
    "defaultrecoption3" => "1 uur per week tot uitschrijven (doorlopend)",
    "defaultrecoption4" => "1 uur per twee weken tot uitschrijven (doorlopend)",
    "defaultrecoption5" => "45 minuten per week tot uitschrijven (doorlopend)",
    "defaultrecoption6" => "30 minuten per week tot uitschrijven (doorlopend)",
    "defaultrecoption7" => "1 uur per dag en eindigt na 1 herhaling",
    "defaultrecoption8" => "1.5 uur per week en eindigt na 4 herhalingen",
    "defaulttext" => "standaard tekst",
    "defaulttrialrecurrenceoption" => "1 uur per dag en eindigt na 1 herhaling",
    "delaccesstoken" => "verwijder toegangscode",
    "delallstudents" => "verwijder alle leerlingen van de lijst",
    "delete" => "verwijder",
    "deleteaccessforstudent" => "verwijder toegang voor leerling",
    "deleteallfutureappointments" => "verwijder alle toekomstige afspraken",
    "deletechecklist" => "verwijder checklijst",
    "deletecourse" => "verwijder cursus",
    "deletecoursefromcoursegroup" => "verwijder cursus van leerlinggroep",
    "deleted" => "verwijderd",
    "deletedateexception" => "verwijder datumuitzondering",
    "deletedefaultchecklist" => "verwijder standaard checklist",
    "deletefailed" => "verwijderen mislukt",
    "deletelibrary" => "verwijder documentbibliotheek",
    "deletelogentry" => "verwijder logboekinvoer",
    "deletemessage" => "verwijder bericht",
    "deleterecurrenceoption" => "verwijder herhaaloptie",
    "deletesavedconcept" => "verwijder opgeslagen concept",
    "deletestudent" => "leerling verwijderen",
    "deletestudentgroup" => "leerlinggroep verwijderen",
    "deletesuccessful" => "verwijderen is gelukt",
    "deletetask" => "deze taak verwijderen",
    "deletetemplate" => "deze template verwijderen",
    "deletethiscourse" => "deze cursus verwijderen",
    "deletethislogentry" => "deze logboekinvoer verwijderen",
    "deletethismessage" => "dit bericht verwijderen",
    "deletethisregistration" => "deze inschrijving verwijderen",
    "deletethisstudent" => "deze leerling verwijderen",
    "deletethisstudentgroup" => "deze leerlinggroep verwijderen",
    "deletetrialrequest" => "verwijder dit proefles verzoek",
    "deletetutor" => "verwijder docent",
    "delselectedstudents" => "verwijder geselcteerde leerlingen uit de lijst",
    "deprecated" => "verouderd",
    "description" => "omschrijving",
    "detachingdocumentfailed" => "loskoppelen document is mislukt",
    "details" => "details",
    "detailurl" => "event detail url",
    "devenvironment" => "devel omgeving",
    "didnotenterresponseforeveryday" => "Je hebt nog niet voor elke dag aangegevens of je beschikbaar bent",
    "directsearch" => "direct zoeken",
    "docnotdeletedbutremovedfromlib" => "het document wordt niet verwijderd maar alleen losgekoppeld van deze bibliotheek",
    "documents" => "document|documenten",
    "doesnotapply" => "niet van toepassing",
    "domain" => "domein",
    "domainname" => "domeinnaam",
    "domainsettings" => "domeininstellingen",
    "dontmove" => "niet uitwijken",
    "download" => "download",
    "doyouwanttocontinue" => "wil je verder gaan",
    "doyouwanttosetadminastutor" => "wil je je admin account ook als docent gebruiken?",
    "drop" => "drop",
    "duedate" => "vervaldatum",
    "duocourse" => "duoles",
    "duolesson" => "duoles",
    "dutch" => "Dutch",
    "edit" => "wijzig",
    "editchecklist" => "wijzig checklijst",
    "editdateexception" => "wijzig datumuitzondering",
    "editdescription" => "wijzig beschrijving",
    "editevent" => "wijzig afspraak",
    "editing" => "aan het wijzigen",
    "editlibrary" => "wijzig leerlingbibliotheek",
    "editlocation" => "wijzig cursuslocatie",
    "editprofile" => "Wijzig profiel",
    "editrecurrenceoption" => "wijzig herhaal optie",
    "editschoolyear" => "wijzig schooljaar",
    "editstudent" => "wijzig leerling",
    "editstudentgroup" => "wijzig leerlinggroep",
    "editstudentlist" => "wijzig leerlinglijst",
    "edittimetable" => "wijzig rooster",
    "edittutor" => "wijziging docentgegevens opslaan",
    "email" => "e-mail",
    "emailaddress" => "e-mail adres",
    "emailcontacts" => "e-mail contacten",
    "emaillistallstudents" => "e-mail lijst van alle leerlingen (in- en uitgeschreven)",
    "emaillistallstudentsminimalone" => "e-mail lijst van alle leerlingen met tenminste 1 actieve cursus",
    "emaillistallstudentsnocourse" => "e-mail lijst van alle leerlingen zonder actieve cursus (oud leerlingen)",
    "emaillists" => "e-mail lijsten",
    "emaillog" => "e-maillog",
    "emailsend" => "e-mail verstuurd",
    "emailsettings" => "e-mailinstellingen",
    "emailstudentswithaccess" => "e-mail leerlingen met toegang",
    "emailthisstudent" => "e-mail deze leerling",
    "emptyisendofschoolyear" => "leeg betekent: tot einde schooljaar",
    "emptyisindefinitely" => "leeg betekent: toon altijd (sticky)",
    "enddate" => "einddatum",
    "enddateforcoursesubscriptionset" => "einddatum voor cursus inschrijving ingevoerd",
    "enddatenotsamedayasstartdate" => "einddatum is niet op dezelfde dag als de startdatum",
    "endedcourses" => "be&euml;indigde cursus|be&euml;indigde cursussen",
    "endisbeforestart" => "de eind datum/tijd ligt v&oacute;&oacute;r de begin datum/tijd",
    "endisnotinfuture" => "de eind datum/tijd ligt in het verleden",
    "endsafter" => "eindigt na",
    "endsafternrofoccurrences" => "en eindigt na aantal keer",
    "endtime" => "eindtijd",
    "endyear" => "eindjaar",
    "english" => "English",
    "enterday" => "voer dag in",
    "enterdescription" => "Omschrijving invoeren",
    "enterfromtime" => "voer vanaf-tijd in",
    "enterimageurl" => "voer afbeelding URL in",
    "enterlabel" => "label invoeren",
    "entermessage" => "voer bericht in",
    "entertimeslice" => "voer tijdsegment in",
    "entertotime" => "voer tot-tijd in",
    "enterurl" => "URL invoeren",
    "error" => "fout",
    "error403subtitle" => "u heeft geen toegang tot deze pagina",
    "error403title" => "er is een fout opgetreden",
    "error404subtitle1" => "neem contact op met",
    "error404subtitle2" => "om dit probleem op te lossen",
    "error503subtitle" => "service is in onderhoud, we zijn snel terug",
    "error503title" => "service is tijdelijk niet beschikbaar",
    "erroraddingstudents" => "fout bij toevoegen van leerlingen",
    "errorendatesmallerthanstartdate" => "einddatum moet groter zijn dan de startdatum",
    "errorfetchingappointmentsoftoday" => "fout bij ophalen afspraken van vandaag",
    "errorfetchingcoursedata" => "fout bij ophalen cursusgegevens",
    "errorfetchingdomaindata" => "fout bij ophalen domeingegevens",
    "errorfetchingfirstappointment" => "fout bij ophalen van je eerstvolgende afspraak",
    "errorfetchingtrialcourserelations" => "fout bij ophalen proeflesrelaties",
    "errorgrantingaccess" => "fout bij verlenen toegang",
    "errorhidingalert" => "fout bij verbergen alarm",
    "errorloadingattendanceoptions" => "fout bij laden presentieopties",
    "errorloadingcoursegroups" => "fout bij laden van cursusgroepen",
    "errorloadingcourses" => "fout bij laden van cursussen",
    "errorloadingdata" => "fout bij laden gegevens",
    "errorloadinglocations" => "fout bij laden van locaties",
    "errorloadingrecurrenceoptions" => "fout bij laden herhaalopties",
    "errorloadingschoolyears" => "fout bij laden schooljaren",
    "errorloadingstudentlist" => "fout bij laden leerlinglijst",
    "errorloadingstudentlists" => "fout bij laden leerlinglijsten",
    "errorloadingstudents" => "fout bij laden van leerlingen",
    "errorloadingtimetables" => "fout bij laden tijdtabel informatie",
    "errorloadingtutors" => "fout bij laden van docenten",
    "errorremovingstudents" => "fout bij verwijderen van leerlingen",
    "errorretrievingalerts" => "fout bij laden alarmering",
    "errorrevokingaccess" => "fout bij intrekken toegang",
    "errorsavingcourserelation" => "fout bij opslaan cursusrelatie",
    "errorsavingdata" => "fout bij opslaan gegevens",
    "errorsavingnewdatetime" => "fout bij opslaan nieuwe datum / tijd",
    "errorsavingpin" => "fout bij opslaan pin",
    "errorsavingregistration" => "fout bij opslaan cursusinschrijving",
    "errorsendingmultiicsfile" => "fout bij verzenden uitnodiging",
    "errortitle" => "er is een fout opgetreden",
    "errorunhidingalert" => "fout bij herstellen alarm",
    "errorupdatingmandatenumber" => "wijzigen mandaatnummer mislukt",
    "eventconflicts" => "kalender conflicten",
    "eventconflictswith" => "cursus :coursename op :eventdate conflicteert met :reason",
    "events" => "afspraak|afspraken",
    "eventsadded" => "afspraak toegevoegd|afspraken toegevoegd",
    "eventsAndDateExceptions" => "afspraken en datumuitzonderingen (laagjes)",
    "eventsholidaysleave" => "events, vakanties en verlof",
    "evenweeks" => "even weken",
    "everyappointmentuntilandincluding" => "alle afspraken vanaf deze, tot en met",
    "everybody" => "iedereen",
    "everyday" => "elke dag",
    "everyone" => "iedereen",
    "everyweek" => "elke week",
    "expireson" => "tonen tot en met",
    "explainactivestaff" => "actieve medewerkers: niet geblokkeerd en geen einddatum in het verleden",
    "explainactivestudents" => "actieve leerlingen: leerlingen die op minstens 1 cursus zijn ingeschreven",
    "explainaddressfields" => "Deze velden worden gebruikt om e-mail te ondertekenen. ",
    "explainadultthreshold" => "Bij welke leeftijd wordt de leerling als 'volwassen' beschouwd? Bijv. voor toe te passen BTW tarief.",
    "explainallowedipadd" => "Deze ip adressen geven toegang tot het info bord waar locatie bezetting op wordt getoond. Je kunt meer adressen toevoegen door ze te scheiden met een komma.",
    "explainarchivecourse" => "Als een cursus niet meer relevant is maar in het verleden wel leerlingen heeft gehad,<br> kun je m verbergen van de meeste pagina's door te archiveren.<br>Gebruik de archief pagina onder 'rapporten' om de cursus weer terug te zetten.",
    "explainarrowtoaddtoschedule" => "Met de pijltoets plaats je je beschikbare tijden per dag in de rechterkolom. Is je lijst compleet in de rechterkolom, druk dan op opslaan",
    "explainatleastonecourse" => "leerlingen die minimaal 1 actieve cursus hebben, einddatum is leeg of in de toekomst.",
    "explainattendanceoptions" => "Deze opties kunnen door de docent of de administratie worden gebruikt om presentie of absentie van leerlingen aan te geven. De opties kunnen alleen worden verwijderd als ze nog nergens in gebruik zijn.",
    "explainautoapply" => "wordt automatisch gekoppeld bij aanmaken van een nieuwe leerling",
    "explainbroadcastmessage" => "Dit is een bericht voor de hele school. Het wordt zichtbaar voor alle leerlingen die Classy gebruiken.",
    "explaincalendarcolor" => "De kalenderkleur wordt gebruikt om de datumuitzondering in de kalender<br>te kleuren, zowel op het web als in Class. Je kunt dit gebruiken om de<br>kalender in de themakleuren van de website te houden.<br>Om deze mogelijkheid te gebruiken moet de optie <strong>Blokkeert lesplanning</strong><br>bij wijzigen van de gebeurtenis <strong>uit</strong> staan! ",
    "explainConflictsDEEvents" => "totaal aantal events die binnen deze datumuitzondering vallen plus<br>het aantal dat voor de kalender is vastgezet (sticky) in de plankaart.<br>Dit wordt groen getoond als alle conflicten zijn vastgezet, of er geen<br>conflicten zijn. Klik op het nummer label voor meer informatie. ",
    "explaincoursechange" => "Bij eventuele prijswijzigingen brengen we je tijdig op de hoogte",
    "explaincoursegroup" => "Elke cursus behoort tot een cursusgroep, zoals \"privelessen\" of \"creatief\".<br/>Indien van toepassing: gebruik dezelfde groepering als op je website.",
    "explaincoursenottargetcourse" => "deze leerling volgt een cursus die leidt naar de cursus<br>die in deze leerlinggroep wordt gegeven",
    "explaincourserelationships" => "Deze cursusses en proeflessen leiden naar deze cursus. Je kunt deelname aan deze leerlinggroep ook op basis van deze cursussen vastleggen",
    "explaincoursetaxrateadults" => "belasting tarief voor volwassen leerlingen",
    "explaincreategroups" => "schuif leerlingnamen op elkaar (1) om een groep te creeren of naar een groep (2) om toe te voegen aan de groep",
    "explaindateexceptions" => "niet roosterbare dagen en tijden zoals vakanties en andere tijden waarop geen les kan worden gegeven",
    "explaindefaultsignaturewillbeadded" => "De standaard ondertekening wordt automatisch toegevoegd",
    "explaindeleterestrictionlibrary" => "verwijderen kan alleen als de library niet met leerlingen gedeeld is",
    "explaindeleterestrictionlocation" => "verwijderen kan alleen als de locatie niet in een rooster aanwezig is",
    "explaindeleterestrictionrecoption" => "verwijderen en wijzigen kan alleen als de optie niet gebruikt wordt in een cursus",
    "explaindeleterestrictionschoolyear" => "verwijderen kan alleen als er geen gebeurtenissen zijn geregistreerd op het schooljaar",
    "explaindeleterestrictiontutor" => "verwijderen kan alleen als de docent niet in een rooster aanwezig is",
    "explaindetailurl" => "Deze url wordt gebruikt op de website om details van een event te tonen.<br>Vul de volledige url van de detailpagina in, inclusief https://. <br>Om deze mogelijkheid te gebruiken moet de optie <strong>Blokkeert lesplanning</strong><br>bij wijzigen van de gebeurtenis <strong>uit</strong> staan! ",
    "explainfreezetimespan" => "vink dit uit als je de tijdsspanne van de gebeurtenis ook wilt veranderen",
    "explaingeneratemandatenumber" => "let op: als het het mandaatnummer wordt vastgesteld moet het nummer ook moet worden vastgelegd of bijgewerkt in de financiele administratie en bij de bank.",
    "explainhasaccess" => "geeft aan of de leerling toegang heeft tot het<br>invullen van de planningsvoorkeur pagina",
    "explainifchosentypenotfound" => "Als het gekozen type niet wordt gevonden, gebruikt Class het eerst beschikbare e-mailadres, welk type dan ook. Deze keuze werkt dan ook alleen als de student meerdere e-mailadressen beschikbaar heeft.",
    "explainignoreforalerts" => "Wil je gewaarschuwd worden op het dashboard als deze<br>gebeurtenis een conflict heeft met een lesafspraak?<br>Los van deze setting wordt de gebeurtenis altijd getoond<br>in de alarmen in de plankaart van leerlingen.",
    "explainignorenovalidprefs" => "de leerling heeft in de afgelopen :nrofmonth maanden geen planningvoorkeur ingevoerd. Als je deze waarschuwing negeert, wordt deze leerling als \"geen voorkeur\" en dus vrij in te plannen beschouwd|de leerlingen hebben in de afgelopen :nrofmonth maanden geen planningvoorkeur ingevoerd. Als je deze waarschuwing negeert, worden deze leerlingen als \"geen voorkeur\" en dus vrij in te plannen beschouwd",
    "explainimageusageinemail" => "Kopieer hier de url van de afbeelding. Gebruik vervolgens de knop \"insert media\" in de editor om de afbeelding in de e-mail te plaatsen.",
    "explaininactivestudents" => "leerlingen die nog niet of niet meer op een cursus zijn ingeschreven",
    "explainistrialcourse" => "Dit is een speciale groep voor cursussen die een ander tarief hebben (gratis of gereduceerd).<br>Ze worden ook anders getoond op de website.",
    "explainkeepscheduletimerestriction" => "Kies je deze optie(s), dan zien we dat als je eerste voorkeur. We proberen rekening te houden met je wensen.<br/>&nbsp;&nbsp;&nbsp;Als je je lesvorm wilt wijzigen, neem dan even contact met ons op.",
    "explainleavesopennotrespondedyet" => "hou toegang open voor iedereen die in de laatste :days dagen nog niet ingevuld heeft",
    "explainleavesopennotrespondedyetextend" => "voor alle leerlingen die geen voorkeur ingevuld hebben in de afgelopen :days dagen. Ouder dan :days2 dagen beschouwen we als informatie van vorig jaar, dus \"heeft nog niet geantwoord\".",
    "explainmakeadminatutor" => "Je kunt je <em>admin</em> account <em>docent</em> rechten geven om deze rollen in 1 account te verenigen. Ga daarvoor naar je profielpagina",
    "explainminmaxgroupsize" => "specificeer de minimale en maximale groepgrootte als je deze cursus in groepsverband geeft.<br>Laat beide waarden op \"1\" staan als het een individuele cursus is.<br>Deze waarden worden gebruikt bij het samenstellen van leerlinggroepen.",
    "explainnewemailsignrequest" => "indien je de e-mail naar een ander adres wilt versturen of een andere aanhef wilt gebruiken, kun je het hier wijzigen.<br>Als je meer dan 1 e-mailadres in wilt voeren moet je ze van elkaar scheiden met een komma",
    "explainnoaccesstoken" => "Dit zou niet meer mogen gebeuren. Meld dit bij Scolavisa om het probleem op te lossen",
    "explainnotthesameasenddate" => "Let op, dit is niet hetzelfde als het invullen van een uitschrijfdatum! Deze actie verwijdert de inschrijving definitief!",
    "explainnrofappointmentsforyear" => "aantal toekomstige afspraken ingeroosterd / totaal aantal afspraken ",
    "explainnrofevents" => "aantal lesafspraken: 'toekomstige' / 'alle' excl blokkers ('alle' inc blokkers) ",
    "explainonlyoneconcept" => "Op dit moment kan er precies 1 concept op je lokale computer staan. Als je Class op een andere computer opent, zie je het opgeslagen concept van je andere computer NIET! In een latere versie van Class kun je hier ook templates selecteren.",
    "explainonlysingleevent" => "Dit veld kan alleen gewijzigd worden voor een <em>enkele afspraak</em>",
    "explainopennotrespondedyet" => "open toegang voor iedereen die nog niet ingevuld heeft in de afgelopen :days dagen",
    "explainopennotrespondedyetextend" => "voor leerlingen de voorkeur NIET ingevuld hebben in de afgelopen :days dagen. \t Ouder dan :days2 dagen beschouwen we als informatie van vorig jaar, dus \"heeft nog niet geantwoord\".",
    "explainplanblocking" => "Standaart blokkeert een datumuitzondering de automatische en handmatige lesplanning.<br>Als deze optie uit staat, wordt de datumuitzondering alleen gebruikt om een gebeurtenis<br>op de kalender te tonen. De planning wordt er niet door beinvloed. De alarmering is in dat<br>geval ook automatisch uitgeschakeld",
    "explainpreferedtime" => "We proberen zoveel mogelijk rekening te houden met je voorkeur",
    "explainpricesubadult" => "Voor de gepubliceerde prijslijst,<br>meestal een mooi rond getal in de buurt van de prijs exclusief BTW",
    "explainpwreset" => "Het nieuwe wachtwoord wordt naar het e-mail adres van de docent gestuurd. De docent heeft een geldig wachtwoord nodig om toegang te krijgen tot ClassE.<br>De docent kan indien gewenst geblokkeerd worden met behulp van de 'blokkeer' vlag in het wijzigscherm van de docent.",
    "explainreschedule" => "Let op: CLASS zal alleen afspraken verplaatsen als die dezelfde weekdag en tijd hebben als de oorspronkelijke weekdag en tijd van deze serie afspraken. Eerder gewijzigde afspraken blijven staan omdat ze niet langer deel zijn van de serie.",
    "explainresetpw" => "Het wachtwoord van de docent wordt hiermee gewijzigd in het standaard wachtwoord van de school. Na inloggen moet de docent het wachtwoord direct wijzigen om verder te kunnen met CLASS.",
    "explainschedulethreshold" => "aantal dagen dat laatste keer invullen beschikbaarheid geleden mag zijn, waarna de informatie niet meer als geldig wordt gezien. Zo wordt voorkomen dat bijv. invullen van vorig jaar wordt gezien als 'reeds ingevuld'.",
    "explainsetuppage" => "CLASS heeft nog enkele acties nodig om alle programmafuncties uit te kunnen voeren",
    "explainsignedoffstudents" => "leerlingen die voorheen een cursus hebben gehad waarvan de einddatum in het verleden ligt. Mogelijk is een andere cursus wel actueel.",
    "explainsignrequestemail" => "Je inschrijving is bijna klaar. <ul><li>Met jouw druk op de knop bevestig je je inschrijving bij :schoolname. Klik hieronder op 'bevestig mijn inschrijving'.</li> <li>Je browser opent dan een nieuwe pagina (CLASS) waarin je een vinkje moet zetten. Daarna druk je op Opslaan knop om je inschrijving aan ons te versturen. Als dat gelukt is, opent zich een scherm dat dit bevestigt.</li></ul>Werkt de knop niet? Kopieer dan deze link in je browser en druk op Enter.",
    "explainsignrequestpage" => "Hieronder staan je gegevens zoals wij die in onze administratie hebben opgenomen.<br/>Als alles hieronder klopt, klik dan het selectievakje aan en druk op opslaan",
    "explainstudentcontactlabel" => "bijv. \"prive\" of \"ouders\"",
    "explainstudentlist" => "<span class=\"label label-primary\" >&nbsp;Deze achtergrond&nbsp;</span> betekent dat de leerling lid is van deze lijst.<br/>Klik op de lijstnaam om het lidmaatschap te wijzigen.",
    "explainstudentsindividualaccess" => "geef expliciet toegang aan deze leerlingen",
    "explainstudentsinstudentgroupdirectaccess" => "leerlingen die lid zijn van deze groepen krijgen direct toegang, zonder dat je de bibliotheek expliciet met ze hoeft te delen",
    "explainstudentsnocourses" => "leerlingen die geen actieve cursus (meer) hebben.",
    "explainstudentswithcoursedirectaccess" => "leerlingen met deze cursus(sen) krijgen direct toegang, zonder dat je de bibliotheek expliciet hoeft te delen met die leerlingen",
    "explainstudentswithcoursegroupdirectaccess" => "leerlingen met een cursus in deze groep krijgen direct toegang, zonder dat je de bibliotheek expliciet met ze hoeft te delen",
    "explaintrialcourserelation" => "sleep de cursussen naar de proeflessen om aan te geven voor welke cursus de proefles bedoeld is. Dat kunnen er meer zijn. Deze relatie wordt gebruikt bij mogelijke samenstelling van leerlinggroepen",
    "explaintrialgroup" => "Als je deze groep markeert als proefles groep, kun je de cursussen in deze groep op je website tonen als proefles.",
    "explaintrialsettrue" => "Cursussen in deze groep kunnen als proefles worden getoond op de website",
    "explainunarchivedcourses" => "klik op de cursusnaam om een cursus uit archief te halen. Gebruik vervolgens de archiefknop in het wijzigscherm van de cursus.",
    "explainusefor" => "geef aan of het studentcontact specifiek voor een of meer van de<br>onderstaande doelen gebruikt moet worden, bijvoorbeeld in mailing",
    "explainvariables" => "je kunt deze variabelen gebruiken in de mailtemplate. Class vervangt de variabelen door de juiste waarde bij versturen van de e-mail.",
    "explainwarnbeforeadult" => "hoeveel dagen voor een verjaardag waarop een leerling volwassen wordt wil je een waarschuwing zien op het dashboard (prijs kan veranderen door ander belasting tarief). Voer \"0\" in om de waarschuwing uit te schakelen.",
    "explainwarnbeforebirthday" => "hoeveel dagen voor een verjaardag van een leerling wil je een waarschuwing zien op het dashboard",
    "explainwordpresscode" => "deze code kun je gebruiken in een WordPress pagina<br>om bezoekers op dit event in te laten schrijven. Volg<br>de instructies op de WordPress plugin pagina om de<br>code te gebruiken.",
    "exportstudentprefs" => "exporteer planningvoorkeuren leerlingen",
    "exporttoexcel" => "export naar spreadsheet",
    "extraemailaddresses" => "extra e-mailadressen",
    "extrainfo" => "extra informatie",
    "failed" => "mislukt",
    "failedtogetdataforregistration" => "ophalen registratie gegevens mislukt",
    "fieldmaynotexceedlength" => "het veld :fieldname kan maximaal :max tekens bevatten",
    "filelist" => "bestandslijst",
    "filemanagement" => "bestandsbeheer",
    "files" => "bestand|bestanden",
    "fileupload" => "bestand upload",
    "fillstartdattimeabove" => "voer het datum segment in hierboven",
    "filter" => "filter",
    "filteron" => "filter op",
    "findDocumentToLinkToLibrary" => "zoek een bestaand document of weblink om te koppelen aan deze bibliotheek",
    "findexistingdocument" => "zoek een document om aan de bibliotheek te koppelen",
    "finish" => "be&euml;indigen",
    "firstchooseplandetails" => "kies eerst de planningdetails",
    "firstchoosestudentorgroup" => "kies eerst een leerling of leerlinggroep",
    "firstname" => "voornaam",
    "firstregistration" => "eerste inschrijving",
    "fixthishere" => "los dit hier op",
    "followingtaskswillbedeleted" => "de volgende taak worden verwijderd|de volgende taken worden verwijderd",
    "followlink" => "Open link",
    "followuprelations" => "opvolgrelatie",
    "for" => "voor",
    "foradults" => "voor volwassenen",
    "forallstudents" => "voor alle leerlingen",
    "forcourse" => "voor cursus",
    "forcourseparticipants" => "voor cursusdeelnemers",
    "formatphonenumber" => "formatteer telefoonnummer",
    "formerstudents" => "oud-leerling|oud-leerlingen",
    "fortheschool" => "voor de school",
    "foryouth" => "voor jongeren",
    "frequency" => "frequentie",
    "from" => "vanaf",
    "fromemail" => "van",
    "fromtimegreaterthanendtime" => "vanaf-tijd is groter dan tot-tijd",
    "full" => "volledige",
    "functions" => "functies",
    "futureappointments" => "toekomstige afspraak|toekomstige afspraken",
    "futureappointmentswillbedeleted" => "let op: afspraken na de einddatum worden verwijderd",
    "futuremessage" => "toekomstig bericht",
    "generate" => "genereren",
    "genericdata" => "algemene gegevens",
    "givemetutorrights" => "geef me docent rechten",
    "goto" => "ga naar",
    "gotoalertpage" => "ga naar de alarmeringpagina",
    "gotofulltimetable" => "ga naar het volledige lesrooster",
    "gottodashboard" => "ga naar het dashboard",
    "grantaccess" => "verleen toegang",
    "grantaccesstoallstudents" => "verleen toegang aan alle leerlingen",
    "groupcourse" => "groepscursus",
    "groupcourseextend" => "deze cursus wordt in groepsverband gegeven. Klik om de deelnemers te zien.",
    "groupfilter" => "groep filter",
    "groupname" => "groepsnaam",
    "groups" => "groep|groepen",
    "groupscanonlybedeletedif" => "een groep kan alleen verwijderd worden als er geen cursus aan gekoppeld is.",
    "handled" => "behandelde",
    "handledtrialcourserequests" => "afgehandelde proeflesverzoeken",
    "hasaccess" => "heeft toegang",
    "hasenddate" => "heeft einddatum",
    "hasfutureenddate" => "einddatum in de toekomst",
    "hasfutureevents" => "heeft toekomstige afspraken",
    "hasnoaccess" => "heeft geen toegang",
    "hasnoappointmentfortrialcourse" => "proeflesaanvragen zonder een ingeplande proefles",
    "hasnofollowupcourse" => "proeflesaanvragen met een ingeplande proefles maar zonder een vervolgcursus ",
    "helpcourseregistrationendsafter" => "na deze datum zullen geen afspraken worden ingepland",
    "herebeappointmentsbeforesaving" => "Hier komen de afspraken opgesomd die gepland worden. Je kunt ze nog even inzien voordat je ze opslaat.",
    "heresyourpersonalaccesstoken" => "Hier is je nieuwe persoonlijke accesstoken. Dit token wordt hierna niet meer getoond, dus raak het niet kwijt! Je kunt dit token gebruiken om beveiligde toegang te geven tot administratieve informatie uit CLASS.",
    "hexcolor" => "kleur",
    "hhmm" => "uu:mm",
    "hidden" => "verborgen",
    "hide" => "verberg",
    "highpriority" => "hoge prioriteit",
    "holidaytable" => "vakanties en verlof",
    "hours" => "uur|uren",
    "hoverclickdatetoedit" => "klik op de 'vanaf' datum om die datum te wijzigen",
    "hoverclickseriescolortoedit" => "klik op de serie kleur om afspraken in die serie te wijzigen",
    "howmanyeventsdoyouwanttoplan" => "hoeveel achtereenvolgende afspraken wil je inplannen? '0' betekent het hele schooljaar.",
    "hundredmostrecententries" => "max 100 meest recente mailtjes",
    "iagree" => "ik bevestig hierbij mijn inschrijving met bovenstaande gegevens.",
    "iagreecheckboxnotticked" => "het akkoord-selectievakje stond niet aan",
    "icon" => "icon",
    "ifagreecheckbox" => "als deze inschrijving klopt, klik dan dit selectievakje aan",
    "ifoccupiedmoveto" => "uitwijken naar deze locatie als de gewenste locatie bezet is",
    "ignorecurrentschedule" => "negeer de huidige planning",
    "ignoreforalerts" => "negeer voor alarmeringen",
    "ignorethisrow" => "negeer deze regel",
    "imageurl" => "Afbeelding via URL",
    "importICS" => "import ICS",
    "in" => "in",
    "inactivetutors" => "oud-docenten",
    "inbox" => "postvak in",
    "including" => "inclusief",
    "incompletechecklistspresent" => "onvolledige checklist(s) aanwezig",
    "individual" => "individueel",
    "individualcourse" => "individuele cursus",
    "individualcourseextend" => "dit is een individuele cursus",
    "individualstudentstobescheduled" => "In te plannen individuele leerlingen",
    "individualstudentswanttokeepschedule" => "individuele leerlingen die de huidige planning willen behouden",
    "info" => "extra info",
    "ingroup" => "in groep",
    "ingroupsoverwostudents" => "in groepen van meer dan 2 leerlingen",
    "intermediateplanning" => "voorlopige planning",
    "invoiceforsinglelesson" => "ingeplande losse les factureren",
    "iprefertokeepcurrenttime" => "ik wil, indien mogelijk, deze tijd behouden",
    "isatrialcourse" => "is een proefles",
    "isblockedforloginclasse" => "is geblokkeerd voor ClassE",
    "isgroupcourse" => "is groepsles",
    "isnotatrialcourse" => "is geen proefles",
    "isnotblockedforloginclasse" => "is NIET geblokkeerd voor ClassE",
    "isrecurringappointment" => "is herhalend",
    "istrialcourse" => "is proefles",
    "istrialcoursefor" => "is proefles voor",
    "istrialgroup" => "is proeflesgroep",
    "items" => "item|items",
    "itsmybirthday" => ":name is vandaag jarig!",
    "jumpto" => "spring naar",
    "jumptopage" => "bevestig dat je naar pagina :target wilt springen",
    "keepctrlpressformultiselect" => "houd de Ctrl toets ingedrukt om meerdere te selecteren",
    "keepcurrentschedule" => "huidige tijd behouden",
    "keeptimespan" => "behoud tijdsspanne",
    "kindregards" => "hartelijke groeten",
    "label" => "label",
    "language" => "nl",
    "lastactivity" => "laatste keer actief",
    "lastknowndate" => "laatst bekende datum",
    "lastmonth" => "vorige maand",
    "lastname" => "achternaam",
    "lastnameincorrect" => "achternaam niet juist",
    "lasttimeemailed" => "laatste keer gemaild",
    "lasttimefilledin" => "laatst ingevuld",
    "lastupdate" => "laatste wijziging",
    "lastweek" => "Afgelopen week",
    "leaveemptyforcontinuous" => "laat leeg of '0' voor 'tot opzeggen'",
    "lessonplanning" => "lesplanning",
    "libraries" => "bibliotheek|bibliotheken",
    "librarydata" => "bibliotheek informatie",
    "libraryisnotshared" => "de bibliotheek is niet gedeeld",
    "librarysharing" => "bibliotheek delen",
    "limitingnumberofrepeatstocoursedefinition" => "aantal herhalingen beperkt tot :count vanwege de opgegeven herhalingen van de cursus",
    "limitoftrialaccountreached" => "Je bent aan de limiet van je proefaccount!",
    "limitoftrialaccountreachedcourse" => "Je hebt het maximale aantal cursussen ingevoerd voor een proefaccount!",
    "limitoftrialaccountreachedstudent" => "Je hebt het maximale aantal leerlingen ingevoerd voor een proefaccount!",
    "limitoftrialaccountreachedtutor" => "Je hebt het maximale aantal docenten ingevoerd voor een proefaccount!",
    "link" => "koppel",
    "links" => "link|links",
    "list" => "lijst",
    "listlibraries" => "lijst bibliotheken",
    "listscanonlybedeletedif" => "lijsten kunnen alleen verwijderd worden als er geen leerlingen aan gekoppeld zijn",
    "liststudentsoncourses" => "Lijst leerlingen op cursus",
    "loadingtagsfailed" => "ophalen van de tags mislukt",
    "locationdata" => "locatie informatie",
    "locationdeleted" => "locatie verwijderd",
    "locationicon" => "Locatie icon",
    "locationoccupied" => "cursuslocatie is al in gebruik",
    "locations" => "cursuslocatie|cursuslocaties",
    "locationsmissing" => "geen cursuslocaties gevonden",
    "locked" => "geblokkeerd",
    "logbook" => "logboek",
    "logbookentry" => "logboek invoer",
    "login" => "inloggen",
    "logourl" => "url logo",
    "logout" => "uitloggen",
    "mailallstudents" => "email alle leerlingen met toegang",
    "mailbody" => "mailtekst",
    "mailtemplates" => "mailtemplates",
    "mailtext" => "tekst e-mail",
    "mailto" => "email naar",
    "manage" => "beheer",
    "manageContents" => "Wijzig bibliotheek inhoud",
    "manageSharing" => "Wijzig bibliotheek delen",
    "mandatenumber" => "mandaat nummer",
    "mandatory" => "*",
    "maximum" => "maximale",
    "maxstudentgroupsize" => "max. leerlinggroep grootte",
    "mayalsomakeadminatutor" => "je kunt ook je admin account een docent maken",
    "messages" => "bericht|berichten",
    "messagesaved" => "bericht opgeslagen",
    "messagesentfailed" => "bericht versturen mislukt",
    "messagesentto" => "bericht verstuurd naar :recipient",
    "minimum" => "minimale",
    "minstudentgroupsize" => "min. leerlinggroep grootte",
    "minutes" => "minuut|minuten",
    "missingdata" => "missende gegevens",
    "month" => "maand",
    "months" => "maand|maanden",
    "more" => "meer",
    "my" => "mijn",
    "myappointments" => "mijn afspraken",
    "name" => "naam",
    "namecontactperson" => "naam contactpersoon",
    "new" => "nieuw",
    "newattendanceoption" => "nieuwe presentieoptie",
    "newchecklist" => "nieuwe checklijst",
    "newcontactitem" => "nieuw contact gegeven",
    "newcourse" => "nieuwe cursus",
    "newcoursegroup" => "nieuwe cursusgroep",
    "newcourseregistration" => "nieuwe cursusinschrijving",
    "newcoursetobeplanned" => "nieuw in te plannen cursus",
    "newdateexception" => "nieuwe datumuitzondering",
    "newdatetime" => "niewe datum / tijd",
    "newdaytime" => "nieuwe dag / tijd",
    "newdefaultchecklist" => "nieuwe standaard checklist",
    "neweventcreatedopentosave" => "Open de nieuwe afspraak om op te slaan!",
    "newevents" => "nieuwe afspraken",
    "newgroupforthesestudents" => "nieuwe groep voor de volgende leerling(en)",
    "newlibrary" => "nieuwe bibliotheek",
    "newlocation" => "nieuwe cursuslocatie",
    "newlogentry" => "nieuwe logboekinvoer",
    "newmessage" => "nieuw bericht",
    "newpassword" => "nieuw wachtwoord",
    "newpasswordisrequired" => "...vul het nieuwe wachtwoord in",
    "newpasswordsdonotmatch" => "...de nieuwe wachtwoorden zijn niet gelijk aan elkaar",
    "newplanningsaved" => "nieuwe planning succesvol opgeslagen",
    "newrecurrenceoption" => "nieuwe herhaaloptie",
    "newregistrations" => "nieuwe inschrijvingen",
    "newschoolyear" => "nieuw schooljaar",
    "newstudent" => "nieuwe leerling",
    "newstudentgroup" => "nieuwe leerlinggroep",
    "newstudentgroupcreated" => "nieuwe leerlinggroep gemaakt",
    "newstudentgroupforcourse" => "nieuwe leerlinggroep voor deze cursus",
    "newstudentlist" => "nieuwe leerlinglijst",
    "newtask" => "nieuwe taak",
    "newtemplate" => "nieuwe template",
    "newtemplatelabel" => "label voor de nieuwe template",
    "newtriallessonrequests" => "nieuw proefles verzoek|nieuwe proefles verzoeken",
    "newtutor" => "nieuwe docent",
    "next" => "volgende",
    "nextmonth" => "volgende maand",
    "no" => "nee",
    "noaccess" => "geen toegang",
    "noaccestoken" => "geen accesscode",
    "noactivecoursesnoworfuture" => "geen actieve cursussen nu of in de toekomst",
    "noalerts" => "geen waarschuwingen",
    "noalertsfound" => "geen alarmering gevonden",
    "noappointments" => "geen afspraken",
    "noappointmentsfound" => "geen afspraken gevonden",
    "noappointmentstosend" => "geen afspraken om te versturen",
    "nochangestosave" => "geen wijzigingen om op te slaan",
    "nochecklistdefinitionsfound" => "geen checklijstdefinities gevonden",
    "nochecklistwasadded" => "geen checklist gekoppeld omdat dit op basis van een proeflesverzoek is gemaakt.",
    "noconflicts" => "geen conflicten",
    "nocontactsfound" => "geen contact gegevens gevonden",
    "nocoupledcourses" => "geen gekoppelde cursussen",
    "nocoursefound" => "geen cursus gevonden",
    "nocoursegroupsfound" => "geen cursusgroepen gevonden",
    "nocoursesfound" => "geen cursussen gevonden",
    "nodateexceptionsfound" => "geen datumuitzonderingen gevonden",
    "nodefaultchcklists" => "geen standaard checklists",
    "nodocumentsfound" => "nog geen documenten gevonden",
    "noemailaddressfound" => "geen e-mailadres gevonden",
    "noemailaddressfoundtosendto" => "geen e-mailadres gevonden om naar te versturen",
    "noerrorsorwarnings" => "geen fouten of waarschuwingen",
    "nofreetriallessonevents" => "Er zijn geen proeflesafspraken zonder een gekoppelde taak van dit type.<br/>Gebruik een van de bestaande taken om acties toe te voegen.",
    "nofurtherchangestosave" => "geen verdere wijzigingen om op te slaan",
    "nogroupsfound" => "geen leerlinggroepen gevonden",
    "noinputfor" => "geen informatie ingevoerd voor",
    "nolibrariesfound" => "geen bibliotheken gevonden. Klik op de knop hierboven om je eerste documentenbibliotheek te maken.",
    "nologentriesfound" => "geen logboekinvoer gevonden",
    "nolongerpartofseries" => "maakt geen deel meer uit van de serie",
    "nomessagesfound" => "geen berichten gevonden",
    "nonactivestudents" => "inactieve leerlingen",
    "nonblocking" => "niet blokkerend",
    "none" => "geen",
    "none_newplanning" => "geen",
    "nooldversionsarepreserved" => "oude versies worden niet bewaard",
    "noone" => "niemand",
    "nopermissionautobanktransfer" => "geen toestemming automatisch afschrijven",
    "nopreviousplanningasbase" => "geen voorgaande planning als basis",
    "noresultstoshow" => "geen resultaten te tonen",
    "noschoolyearavailable" => "geen toekomstige schooljaren gevonden",
    "noschoolyearsfound" => "geen schooljaren gevonden",
    "noseriesareselected" => "e-mail verzenden niet mogelijk, er zijn geen afspraakreeksen geselecteerd",
    "nostudentgroupsfound" => "geen leerlinggroepen gevonden",
    "nostudentlistsfound" => "geen leerlinglijsten gevonden",
    "nostudentsareselected" => "e-mail verzenden niet mogelijk, er zijn geen leerlingen geselecteerd",
    "nostudentsfound" => "geen leerlingen gevonden",
    "nostudentsingroup" => "geen leerlingen in deze groep",
    "not" => "niet",
    "notactive" => "overschreven vanwege de instelling \"vastzetten afspraak\" (sticky)",
    "notallseriessareselected" => "niet alle afspraakreeksen zijn geselecteerd. Alleen geselecteerde reeksen worden opgenomen in de e-mail.",
    "notallstudentsareselected" => "niet alle leerlingen zijn geselecteerd. Alleen geselecteerde leerlingen ontvangen de e-mail.",
    "notasks" => "geen taken",
    "notasksfound" => "geen taken gevonden",
    "notavailable" => "niet beschikbaar",
    "notavailableyet" => "nog niet beschikbaar",
    "notavalidtime" => "geen geldige tijd",
    "notconfirmed" => "niet bevestigd",
    "notemplatesfound" => "geen templates gevonden",
    "notes" => "notities",
    "nothingscheduled" => "niets ingeroosterd",
    "notice" => "melding",
    "notimetablesfound" => "geen roosters gevonden",
    "notinplanningtable" => "deze inschrijvingen staan niet in de planningtabel",
    "notopenyet" => "nog niet open",
    "notparticipating" => "op dit moment niet deelnemend",
    "notriallessons" => "geen proeflessen",
    "notsaving" => "opslaan niet gelukt",
    "notsigned" => "niet ondertekend",
    "notsignedbutrequestsent" => "niet ondertekend maar ondertekenverzoek verzonden",
    "notsignednorequestsent" => "niet ondertekend en geen ondertekenverzoek verzonden",
    "notutoringevents" => "geen lesafspraken",
    "notutoringeventstoshowyet" => "nog geen lesafspraken te tonen",
    "notutorsavailable" => "er zijn geen docenten beschikbaar op deze dag",
    "notvalid" => "niet geldig",
    "noupcomingbirthdays" => "geen aanstaande verjaardagen",
    "nowchooseacourse" => "kies een cursus",
    "nrofactivestudents" => "# Huidige leerlingen",
    "nrofappointments" => "aantal Afspraken",
    "nrofevents" => "aantal afspraken",
    "nroffutureappointmentsoftotal" => "Deze inschrijving heeft een actieve planning met :totalEvents geplande afspraken waarvan :blocked worden geblokkeerd, waardoor er in totaal <strong>:totalOccuring</strong> lesafspraken overblijven (<strong>:future</strong> zijn toekomstige afspraken).",
    "nrofitems" => "aantal items",
    "nrofregistrations" => "aantal actieve inschrijvingen",
    "nrofregs" => "aantal inschrijvingen",
    "nrofrepeatstoschedule" => "aantal",
    "nrofstudents" => "# Leerlingen",
    "nrofstudentsingroup" => "# Leerlingen in groep",
    "nroftimes" => "aantal keer",
    "numberofstudents" => "aantal leerlingen",
    "oddevenweeks" => "even/oneven weken",
    "oddweeks" => "oneven weken",
    "of" => "van",
    "ofwhich" => "waarvan",
    "ok" => "OK",
    "oldmessage" => "oud bericht",
    "on" => "op",
    "onlyactive" => "alleen actief",
    "onlypastappointments" => "alleen afspraken gevonden in het verleden (klik de slider om ze te zien)",
    "open" => "open",
    "openchecklist" => "open checklijst",
    "openfullcalendar" => "open volledige kalender",
    "openingstudentpage" => "de leerlingkaart wordt geopend",
    "openlogentry" => "open logboekinvoer",
    "openpage" => "open pagina",
    "openregistration" => "open inschrijving",
    "openstudentfile" => "open studentkaart van :studentName",
    "openstudentgroup" => "open leerlinggroep",
    "openstudentlistpage" => "open de leerlinglijsten pagina",
    "openstudentpage" => "open de leerlingkaart",
    "opentasks" => "openstaande taken",
    "opentimetable" => "open plankaart",
    "opentrialrequests" => "Open proeflesverzoeken",
    "optional" => "optioneel",
    "optionalcommaseparatedemailaddresses" => "andere e-mailadressen, gescheiden door komma's",
    "or" => "of",
    "orgstartdatetime" => "oorspronkelijke start datum/tijd",
    "orjusttypetekst" => "...en pas aan indien gewenst. Of type hier direct de tekst die je wilt",
    "other" => "anders",
    "otherreason" => "andere reden",
    "otherweek" => "om de week",
    "overdue" => "te laat",
    "overrideprice" => "overschrijf prijs",
    "overridetaxrate" => "overschrijf btw tarief",
    "overview" => "overzicht",
    "overviewalarms" => "overzicht alarmering",
    "overviewgroups" => "overzicht groepen",
    "overviewindividualstudents" => "overzicht individuele leerlingen",
    "overviewregistrations" => "overzicht inschrijvingen",
    "overviewregistrationschecklists" => "overzicht inschrijvingen met checklijst",
    "page" => "pagina",
    "participating" => "huidige deelnemer",
    "participatingstudents" => "deelnemende leerlingen",
    "participation" => "deelname",
    "partofday" => "deel van dag",
    "partofseries" => "deel van een serie afspraken",
    "partofseriesbutmoved" => "was deel van een serie afspraken, maar is verplaatst",
    "password" => "wachtwoord",
    "passwordchanged" => "je wachtwoord is gewijzigd",
    "passwordhasbeenreset" => "het wachtwoord is teruggezet naar het standaard wachtwoord",
    "past" => "voorbij",
    "per" => "per",
    "perday" => "dag",
    "permissionautobanktransfer" => "toestemming automatisch afschrijven",
    "permissionsocialshare" => "toestemming social share",
    "permonth" => "maand",
    "persequence" => "reeks",
    "persistevents" => "afspraken vastleggen",
    "persisteventsandcreatetask" => "afspraak vastleggen en maak gelijk taak voor na de proefles",
    "persisteventsdontcreatetask" => "afspraak vastleggen zonder taak na de proefles",
    "personalaccesstokens" => "persoonlijk accesstoken",
    "pertwoweeks" => "2 weken",
    "perweek" => "week",
    "pickarecord" => "kies een record",
    "pinnotvalid" => "pin niet geldig",
    "pinrestrictionsare" => "pin mag alleen cijfers en letters bevatten en moet minimaal 6 tekens lang zijn.",
    "pinsavedcorrectly" => "pin correct opgeslagen",
    "planblocking" => "blokkeert lesplanning",
    "plancard" => "plankaart",
    "plannedappointments" => "geplande afspraken",
    "planningbase" => "bron planning",
    "planningdetails" => "planning details",
    "planningtable" => "planningtabel",
    "planningtarget" => "doel planning",
    "plantables" => "roosterplan",
    "pleaseaddacoursefirst" => "a.u.b. eerst een cursus koppelen",
    "pleaseaddcontent" => "a.u.b. inhoud toevoegen",
    "pleaseaskadminfordetail" => "vraag je CLASS beheerder naar de details met betrekking tot deze e-mail.",
    "pleasecheckyouraddressinfo" => "Controleer je adresgegevens",
    "pleasechoosea" => "kies een",
    "pleasechooseadefaultchecklistorcreateanewone" => "Kies een standaard checklist om te wijzigen of maak een nieuwe aan",
    "pleasechooseanoption" => "kies een optie",
    "pleasechoosearecurrenceoptionorcreateanewone" => "Kies een herhaaloptie om te wijzigen of maak een nieuwe aan",
    "pleasechoosetemplate" => "kies een template, of type een tekst",
    "pleaseconfirmcreatstudentfomtrial" => "CLASS zal een nieuwe leerling invoeren, gebaseerd op dit proeflesverzoek. Is dit akkoord?",
    "pleaseeditindateexceptionsinterface" => "wijzig deze gebeurtenis in de <a href=\":url\">datumuitzondering interface</a>",
    "pleaseenterlastnameascheck" => "Vul de achternaam van de leerling in (als laatste controle)",
    "pleasemakecorrections" => "hier kun je eventuele aanpassingen maken aan de templatetekst",
    "pleasemarkyourpreferedtimesegments" => "Markeer hier je voorkeurstijden voor je les",
    "pleasenote" => "let op",
    "pleasenotestudenthasnoaccess" => "let op: de leerling heeft op dit moment geen toegang tot de openbare versie van deze pagina.",
    "pleasenotifyusifnotcomplete" => "Klopt er iets niet of zijn deze gegevens niet volledig? Geef ons een seintje via:",
    "pleaserefertomanual" => "Kijk in de <a href=\"https://docuclass.scolavisa.eu/doku.php\" target=\"_blank\">handleiding</a> voor meer informatie over dit onderwerp",
    "pleasesavechanges" => "vergeet niet je wijzigingen op te slaan!",
    "pleasesavefirst" => "a.u.b. eerst opslaan...",
    "pleaseselectaday" => "kies aub een dag",
    "pleaseselectstudents" => "Geselecteerde leerlingnamen komen hier bij elkaar te staan. Klik op een rij in de tabel hieronder om een leerlingnaam te selecteren.",
    "pleasestartwithactioninlistorcreatenew" => "begin met een functie in de lijst of maak een nieuwe documentbibliotheek",
    "pleaseusesmallimages" => "gebruik kleine afbeeldingen. (elk max :maxfilesize en :maxtotalfilesize bij elkaar opgeteld). Als je meer dan 1 afbeelding wilt gebruiken, moeten ze allemaal tegelijk toegevoegd worden. Gebruik in dat geval de CTRL toets om meer afbeeldingen te selecteren bij uploaden.",
    "pleasewait" => "even geduld a.u.b....",
    "possiblestudentgroups" => "mogelijke leerlinggroepen",
    "preconditions" => "precondities",
    "preferedtime" => "voorkeurstijd",
    "preference" => "voorkeur",
    "preferencesaved" => "voorkeur opgeslagen",
    "preferencesince" => "voorkeur ingevuld op",
    "preferredlanguage" => "voorkeurstaal",
    "preferredschedule" => "planningsvoorkeur",
    "preferredtimeforlessonat" => "Voorkeurstijd voor je les bij :schoolname",
    "preferstudentemailthataremarkedfor" => "voorkeur voor e-mailadressen van leerlingen die gemarkeerd zijn voor",
    "prefsurl" => "url voorkeuren",
    "prepareplanning" => "voorbereiden planning",
    "preposition" => "tussenvoegsel",
    "present" => "aanwezig",
    "previewsignrequest" => "preview ondertekenverzoek",
    "previous" => "vorige",
    "previousstudents" => "oud-leerlingen",
    "previousstudentsonthiscourse" => "Oud-leerlingen op deze cursus",
    "price" => "prijs",
    "priceExTax" => "ex btw",
    "priceExTaxSubAdult" => "ex btw < :adultthreshold",
    "priceinvoice" => "inc btw",
    "priceper" => "prijs is per",
    "priceresetsuccessfull" => "prijs succesvol hersteld",
    "pricesavedsuccessfull" => "prijs succesvol opgeslagen",
    "printregistrationform" => "print inschrijfformulier",
    "processteachingrequests" => "verwerk lesverzoeken",
    "profile" => "profiel",
    "profilephoto" => "profiel foto",
    "profileupdatedsuccessfully" => "profiel opgeslagen",
    "publishevent" => "Publiceer als event",
    "putonlist" => "zet op lijst",
    "queued" => "in wachtrij geplaats",
    "quickjump" => "quick-jump",
    "quickjumptocourse" => "quick-jump naar een andere cursus",
    "quickjumptostudent" => "quick-jump naar een andere leerling",
    "reactivatealert" => "heractiveer dit alarm",
    "readat" => "gelezen op",
    "readmessage" => "open bericht",
    "readmessages" => "gelezen berichten",
    "reason" => "reden",
    "receivedlessonrequests" => "ontvangen lesverzoeken",
    "recentlogentries" => "recente logboekinvoer",
    "recepient" => "ontvanger",
    "recipient" => "ontvanger",
    "recurrenceoptiondata" => "herhaaloptie informatie",
    "recurrenceoptions" => "herhaaloptie|herhaalopties",
    "recurrences" => "herhaling|herhalingen",
    "refresh" => "verversen",
    "regidnotvalid" => "inschrijving id niet geldig",
    "registerweblink" => "registreer weblink",
    "registration" => "inschrijving",
    "registrationdata" => "inschrijving gegevens",
    "registrationdate" => "inschrijfdatum",
    "registrationdeleted" => "inschrijving is verwijderd",
    "registrationnotsignedafterrequest" => "niet ondertekend na verzoek",
    "registrationnotsignrequested" => "geen ondertekenverzoek verstuurd",
    "registrations" => "inschrijvingen",
    "registrationsaved" => "cursusinschrijving opgeslagen",
    "registrationsopenchecklist" => "inschrijvingen incompleet",
    "regs" => "inschrijving|inschrijvingen",
    "remarks" => "opmerkingen",
    "rememberlongisstrong" => "bedenk: langere wachtwoorden zijn sterker",
    "removedocfromlib" => "verwijder document uit bibliotheek",
    "removedsuccessfully" => "verwijderd",
    "removefromlist" => "verwijder van lijst",
    "removefromschedule" => "verwijder uit schema",
    "removefromthislibrary" => "verwijder uit -deze- bibliotheek",
    "removestudentfromgroup" => "verwijder leerling van groep",
    "removetag" => "verwijder tag",
    "removingtagfailed:" => "verwijderen van de tag is mislukt",
    "repeat" => "herhaal",
    "repeatendisbeforeappointmentend" => "herhaal-einde is eerder dan afspraak-einde",
    "repeatnewpassword" => "herhaal het nieuwe wachtwoord",
    "repeatnewpasswordisrequired" => "...herhaal het nieuwe wachtwoord",
    "repeats" => "herhalingen",
    "repeatslimitedaccordingtocourse" => "herhalingen beperkt tot de herhaaloptie van de cursus",
    "repetitions" => "herhaling|herhalingen",
    "replacedefaulttext" => "vervang standaard tekst",
    "reportactivestudentsgrouped" => "actieve leerlingen, gegroepeerd",
    "reportappointments" => "overzicht afspraken",
    "reports" => "overzichten",
    "reportstudents" => "rapport leerlingen",
    "request" => "verzoek",
    "requestedstartdate" => "voorkeur startdatum",
    "requestsignature" => "je inschrijving bij :customername bevestigen",
    "requestsignatureemail" => "stuur ondertekenverzoek",
    "resendsignrequest" => "stuur ondertekenverzoek opnieuw",
    "reset" => "reset",
    "resetpassword" => "reset wachtwoord",
    "restoresavedconcept" => "herstel opgeslagen concept",
    "retrievingtemplatefailed" => "retrieving chosen template failed",
    "revokeaccess" => "toegang intrekken",
    "revokeaccessfromallstudents" => "toegang voor alle leerlingen intrekken",
    "roomlocation" => "ruimte, locatie",
    "salutation" => "aanhef",
    "salutationforfinancial" => "aanhef voor financieel",
    "salutationforplanning" => "aanhef voor planning",
    "salutationforpromotion" => "aanhef voor promotie",
    "save" => "opslaan",
    "saveconcept" => "concept opslaan",
    "saved" => "opgeslagen",
    "saveeditschoolyear" => "wijziging schooljaar opslaan",
    "saveedittask" => "wijziging taak opslaan",
    "savelogentry" => "loginvoer opslaan",
    "savemandatemumbersuccess" => "opslaan nieuw mandaatnummer is gelukt",
    "savenewschoolyear" => "nieuw schooljaar opslaan",
    "savepreference" => "sla mijn beschikbaarheid op",
    "savesuccess" => "data succesvol opgeslagen",
    "savingfailed" => "gegevens opslaan is niet gelukt!",
    "scheduleassistant" => "Roosterassistent",
    "scheduled" => "ingeroosterd",
    "scheduleproposal" => "roostervoorstel",
    "schedules" => "rooster|roosters",
    "schedulethreshold" => "grens aantal dagen beschikbaarheid",
    "schoolclosed" => "school gesloten",
    "schoolcontactperson" => "school contactpersoon",
    "schoollogo" => "school logo",
    "schoolname" => "schoolnaam",
    "schoolprivacystatementurl" => "url voor privacystatement",
    "schoolratesandconditionsurl" => "url voor tarieven en algemene voorwaarden",
    "schools" => "school|scholen",
    "schooltelephone" => "school telefoon",
    "schoolwebsite" => "schoolwebsite",
    "schoolyear" => "schooljaar",
    "schoolyeardata" => "schooljaar informatie",
    "schoolyeardeleted" => "schooljaar verwijderd",
    "schoolyearmissing" => "schooljaar mist",
    "schoolyears" => "schooljaar|schooljaren",
    "schoolyearsaved" => "schooljaar opgeslagen",
    "search" => "zoek",
    "searchfile" => "zoek bestand",
    "searchfilter" => "zoekfilter",
    "searchfromlist" => "zoek in de lijst",
    "searchstudent" => "zoek leerling",
    "searchstudentbypartofname" => "zoek de leerling waar je gegevens van wil kopieren door minimaal 3 tekens van de naam in the voeren in het zoekveld.",
    "seedateexceptions" => "bekijk datumuitzonderingen",
    "seefulllog" => "bekijk volledige log",
    "seetutorschedule" => "bekijk docentschema",
    "select" => "selecteer",
    "selectall" => "selecteer alles",
    "selected" => "Geselecteerd",
    "selectnone" => "selecteer niets",
    "selectortags" => "kies tags",
    "selectseries" => "selecteer afspraakreeksen",
    "selectstudents" => "selecteer leerlingen",
    "send" => "verzenden",
    "sendagain" => "opnieuw versturen",
    "sendallappointmentascalendarfile" => "verstuur alle afspraken als kalendar bestand",
    "sendappointmentascalendarfile" => "verstuur deze afspraak als kalender bestand",
    "sendappointments" => "verstuur deze afspraken",
    "senddate" => "verzenddatum",
    "sendemail" => "verstuur e-mail",
    "sendicsfile" => "verstuur afspraak|verstuur afspraken",
    "sendingemailfailed" => "e-mail verzenden mislukt",
    "sendmessage" => "verstuur bericht",
    "sendmessagetoclasse" => "verstuur bericht<br>naar ClassE",
    "sendrequesttoemail" => "verstuur verzoek naar e-mail",
    "sendrequesttosalutation" => "gebruik aanhef",
    "sendsignrequest" => "verstuur ondertekenverzoek",
    "sent" => "verzonden",
    "sentemail" => "verzonden e-mail",
    "sequence" => "reeks",
    "serial" => "serie",
    "setadminastutor" => "geef admin account docentrechten",
    "setsticky" => "klik om vast te pinnen: op dit moment komt deze gebeurtenis niet in de kalender, omdat er tegelijkertijd andere blokkerende gebeurtenissen zijn",
    "settings" => "instellingen",
    "settutorrights" => "geef docent rechten",
    "settutors" => "koppel docenten",
    "setupwizard" => "setup wizard",
    "share" => "delen",
    "share_with" => "deel met",
    "sharedwith" => "gedeeld met",
    "sharedwithcoursegroups" => "gedeeld met :count cursusgroep|gedeeld met :count cursusgroepen",
    "sharedwithcourses" => "gedeeld met :count cursus|gedeeld met :count cursussen",
    "sharedwithstudentgroups" => "gedeeld met :count leerlinggroep|gedeeld met :count leerlinggroepen",
    "sharedwithstudents" => "gedeeld met :count leerling|gedeeld met :count leerlingen",
    "sharedwithwholeschool" => "gedeeld met de hele school",
    "sharewithcoursegroups" => "deel met cursusgroepen",
    "sharewithcourses" => "deel met cursussen",
    "sharewithstudentgroups" => "deel met leerlinggroepen",
    "sharewithstudents" => "deel met leerlingen",
    "sharewithwholeschool" => "deel met de hele school",
    "sharing" => "delen",
    "show" => "toon",
    "showall" => "toon alles",
    "showalldateexceptions" => "Toon alle datumuitzonderingen (of alleen de conflicten)",
    "showfrom" => "toon vanaf",
    "showhiddenalerts" => "verborgen waarschuwingen",
    "showme" => "toon",
    "showpastappointments" => "toon ook afspraken uit het verleden",
    "showpastexceptions" => "uitzonderingen uit<br>het verleden tonen",
    "showrecent" => "Toon recente e-mails",
    "showrecipients" => "toon ontvangers",
    "showuntil" => "toon tot",
    "signature" => "ondertekening",
    "signed" => "ondertekend",
    "signedoff" => "uitgeschreven",
    "signedoffforcourse" => "uitgeschreven voor cursus",
    "signedoffstudents" => "leerlingen met uitschrijving voor cursus in het verleden",
    "signedon" => "ondertekend op",
    "signrequestbacklog" => "open ondertekenverzoeken",
    "signrequestnotsend" => "ondertekenverzoek NIET verstuurd",
    "signrequestsend" => "ondertekenverzoek verstuurd",
    "signstatus" => "ondertekenstatus",
    "size" => "grootte",
    "skippedbecauseoccupied" => ":date overgeslagen omdat die bezet is. Reden: :reason",
    "skippeddatetimes" => "overgeslagen datums, tijden",
    "socialmediaprivacyetiquette" => "social media privacy etiquette",
    "solitaryappointment" => "losstaande afspraak",
    "solutionlink" => "oploslink",
    "somestudentsnovalidscheduleprefs" => ":nrofstudents leerling heeft geen geldige planningvoorkeur (meer)|:nrofstudents leerlingen hebben geen geldige planningvoorkeur (meer)",
    "sorry" => "Sorry",
    "sortby" => "sorteer op",
    "sortbylist" => "sorteer op lijst",
    "sortbyname" => "sorteer op naam",
    "sources" => "Bron",
    "sourcesandtarget" => "Bronnen en doel",
    "specialchars" => "een of meer van: ' \" / \\ ~ ! @ # \$ % ^ &  * ( ) _ - + = { } [ ] | ; : < > , . ? of 0-9",
    "specificstudentorgroup" => "een bepaalde leerling of leerlinggroup",
    "staff" => "medewerker|medewerkers",
    "start" => "start",
    "startdate" => "startdatum",
    "startdateoutofrange" => "startdatum is buiten het toegestane bereik",
    "startdatetime" => "start datum/tijd",
    "startingfrom" => "start vanaf",
    "startplanning" => "start planning",
    "starttime" => "start tijd",
    "startupload" => "start upload",
    "startyear" => "start jaar",
    "statementsofagreement" => "akkoordverklaringen",
    "status" => "status",
    "stickyflag" => "sticky vlag",
    "stickyflagupdated" => "sticky vlag bijgewerkt",
    "streetline1" => "straat en huisnummer",
    "streetline2" => "extra adresregel",
    "student" => "leerling",
    "studentaccess" => "leerlingtoegang",
    "studentaccesslink" => "Leerling toegang link",
    "studentcontactdeleted" => "leerlingcontact verwijderd",
    "studentcontactdeletefailed" => "verwijderen van leerlingcontact is niet gelukt",
    "studentcreatedfromtrialrequest" => "leerling aangemaakt aan de hand van proeflesverzoek, openen leerlingkaart...",
    "studentdatacouldnotberemoved" => "leerlinggegevens konden niet verwijderd worden!",
    "studentdataremoved" => "leerlinggegevens verwijderd",
    "studentdeclinedontime" => "leerling heeft tijdig afgezegd",
    "studenteditformtopright" => "rechts boven op de leerlingkaart",
    "studentfile" => "leerlingkaart",
    "studentfirstname" => "leerling voornaam",
    "studentgroupname" => "naam leerlinggroep",
    "studentgroups" => "leerlinggroep|leerlinggroepen",
    "studentgroupstobepscheduled" => "in te plannen leerlinggroepen",
    "studentgroupswanttokeepcurrentschedule" => "leerlinggroepen die de huidige planning willen behouden",
    "studenthasaccess" => "de leerling heeft op dit moment toegang tot de openbare versie van deze pagina.",
    "studenthasaccesstoprefspage" => "De leerling heeft toegang tot de pagina voorkeurstijden via <a href=':href'>deze link</a> (zorg dat je niet ingelogd bent als je deze link wilt testen)",
    "studenthasbeensubscribed" => "De leerling is ingeschreven bij deze leerlinggroep",
    "studenthasbeenunsubscribed" => "De leerling is niet meer ingeschreven bij deze leerlinggroep",
    "studenthasnoaccesstoprefspage" => "De leerling heeft geen toegang tot de pagina voorkeurstijden",
    "studentis" => "deze leerling is :age",
    "studentlistdeleted" => "Leerlinglijst verwijderd",
    "studentlists" => "leerlinglijst|leerlinglijsten",
    "studentname" => "naam leerling",
    "studentnoshow" => "leerling is niet op komen dagen zonder tijdig afzeggen",
    "studentnumbers" => "leerling aantallen",
    "studentprefs" => "leerling voorkeur",
    "students" => "leerling|leerlingen",
    "studentsadded" => "leerlingen toegevoegd",
    "studentsatleastonecourse" => "leerlingen met tenminste 1 actieve cursus",
    "studentsfirstnameis" => "De voornaam van de leerling is",
    "studentsmissing" => "geen leerlingen gevonden",
    "studentsnocourses" => "leerlingen zonder een actieve cursus",
    "studentsoncourse" => "Leerlingen op cursus",
    "studentsonthiscourse" => "leerlingen ingeschreven op deze cursus",
    "studentsremoved" => "leerlingen verwijderd",
    "studentsubscribed" => "leerling ingeschreven",
    "studentswithoutvalidprefs" => "leerlingen zonder geldige planningvoorkeur",
    "studentswithvalidprefs" => "leerlingen met geldige planningvoorkeur",
    "studenttasks" => "taken met betrekking tot deze leerling",
    "studentturns" => "deze leerling wordt :age",
    "studentunsubscribed" => "leerling uitgeschreven",
    "studentwoemailaddress" => "leerling zonder e-mailadres",
    "studentworegistration" => "leerling zonder cursus inschrijving",
    "subject" => "onderwerp",
    "succeeded" => "gelukt",
    "success" => "gelukt",
    "successfullysavednewdatetime" => "nieuwe datum / tijd opgeslagen",
    "successfullysendappointments" => "afspraken verstuurd",
    "suggestion" => "suggestie",
    "sum" => "som",
    "tagaddedsuccess" => "tag toegevoegd",
    "tags" => "tag|tags",
    "targetedmailcontacts" => "doel e-mail contacten",
    "targetgroupsize" => "doel groepsgrootte",
    "taskclosedby" => "taak afgesloten door",
    "taskdata" => "taakgegevens",
    "taskdeleted" => "taak verwijderd",
    "taskenddate" => "einddatum taak",
    "tasks" => "taak|taken",
    "taskstartdate" => "startdatum taak",
    "tasktype" => "taaktype",
    "tax" => "BTW",
    "taxrate" => "BTW tarief %",
    "taxrateresetsuccessfull" => "BTW succesvol hersteld",
    "taxratesavedsuccessfull" => "BTW succesvol opgeslagen",
    "telephone" => "telefoon",
    "telephoneextra" => "2e telefoonnummer",
    "telephonenumber" => "telefoon nummer",
    "template" => "template",
    "templates" => "templates",
    "templatetarget" => "doel template",
    "templatetext" => "template tekst",
    "termsandconditions" => "algemene voorwaarden",
    "termsandconditionsapply" => "de algemene voorwaarden van :customerName zijn van toepassing.",
    "testenvironment" => "test omgeving",
    "testlink" => "test deze link",
    "thanksforregistering" => "dank je voor je inschrijving bij :schoolname",
    "thankyou" => "hartelijk dank",
    "thefilewillberenewedineverylibrary" => "het bestand wordt vernieuwd in elke bibliotheek waar het deel van uitmaakt",
    "thereareconflicts" => "er zijn agenda-conflicten",
    "thereisnochecklist" => "er is geen checklijst",
    "thisandfutureevent" => "deze en toekomstige afpraken",
    "thiscannotbeundone" => "deze actie kan niet ongedaan worden gemaakt",
    "thischangeneedstobesaved" => "deze wijziging moet nog opgeslagen worden",
    "thiscourseplanning" => "roostering voor deze cursus",
    "thisevent" => "deze afspraak",
    "thisfieldismandatory" => "dit veld is verplicht",
    "thisinformationisinavailableouradministration" => "De volgende gegevens zijn bij ons bekend en worden gebruikt voor het plannen van de lessen.",
    "thisisatrialrequeststudent" => "deze leerling is automatisch ingevoerd door CLASS naar aanleiding van een proefles verzoek",
    "thismonth" => "deze maand",
    "thissubscriptionhasactiveplanning" => "deze inschrijving heeft een actieve planning",
    "thissubscriptionhasnoactiveplanning" => "deze inschrijving heeft geen actieve planning",
    "time" => "tijd",
    "timeisalways235959" => "tijd is altijd 23:59:59, de hele dag",
    "timeisempty" => ":field tijd is leeg",
    "timeisnotvalid" => ":field tijd is niet geldig",
    "timeistooearly" => ":field tijd is te vroeg",
    "timeonce" => "keer (eenmalig)",
    "times" => "keer",
    "timesegment" => "tijdsegment",
    "timesegmentnotontutorsschedule" => "lestijd is niet (volledig) binnen het werkschema van de leerkracht",
    "timesegmentnotontutorsscheduleday" => "lestijd is niet (volledig) binnen het werkschema van de leerkracht (verkeerde dag)",
    "timesegmentnotontutorsscheduletime" => "lestijd is niet (volledig) binnen het werkschema van de leerkracht (verkeerde tijd)",
    "timespan" => "tijdsspanne",
    "timetabledata" => "lesrooster informatie",
    "timetables" => "lesrooster|lesroosters",
    "timeunit" => "tijdeenheid",
    "title" => "titel",
    "to" => "aan",
    "tobeplanned" => "in te plannen",
    "tobescheduled" => "in te roosteren",
    "toggleall" => "alles aan/uit zetten",
    "toggletoshowallevents" => "klik om alle afspraken te tonen",
    "toggletoshowonlyfutureevents" => "klik om alleen toekomstige afspraken te tonen",
    "tools" => "tools",
    "toomanyrepeatsfordateexception" => "te veel herhalingen, maximum overschreden voor datumuitzonderingen",
    "tostartorvieplanning" => "om te starten met plannen of om het rooster te bekijken",
    "totalnrofcourses" => "totaal aantal cursussen",
    "totalnrofstudents" => "totaal aantal leerlingen",
    "totalnrofstudentsonthislist" => "totaal aantal leerlingen op deze lijst",
    "totals" => "totaal|totalen",
    "totalsinthisscreen" => "totalen in dit scherm",
    "totaltimetoolow" => "uur. Dat is erg weinig om mee te gaan plannen. We hopen dat je nog wat extra mogelijkheden in kunt vullen!",
    "totimeshouldbegreaterthanfromtime" => "de \"vanaf\" tijd moet groter zijn dan de \"tot\" tijd",
    "trialalreadyhascoupledstudent" => "deze proefles aanvraag heeft al een gekoppelde leerling",
    "triallesson" => "proefles",
    "triallessonrequests" => "proefles verzoek|proefles verzoeken",
    "triallessons" => "proeflessen",
    "triallimitcoursesreached" => "proef limiet bereikt: courses",
    "triallimitstudentsreached" => "proef limiet bereikt: leerlingen",
    "triallimittutorsreached" => "proef limiet bereikt: docenten",
    "trialoptionmissing" => "trial option mist",
    "trialrequest" => "proefles aanvraag",
    "trialrequestdata" => "proefles aanvraag informatie",
    "trialrequests" => "proefles aanvraag|proefles aanvragen",
    "tryagainignorconflicts" => "probeer opnieuw en negeer docent en locatie conflicten",
    "tryingfallbacklocation" => "Locatie is bezet. CLASS probeert of de reserve locatie vrij is.",
    "tryingtofindnewdatetime" => "CLASS probeert een nieuwe datum/tijd te vinden.",
    "tutoralreadyteaching" => "docent geeft op dat moment al les",
    "tutorattached" => "Docent gekoppeld aan de cursusgroep",
    "tutordata" => "docentinformatie",
    "tutordeleted" => "docent verwijderd",
    "tutoredin" => "les in",
    "tutoringevents" => "lesafspraken",
    "tutornotactive" => "docent heeft geen actief account",
    "tutornotavailable" => "docent afwezig",
    "tutornotavailableaccordingtoschedule" => "docent afwezig volgens zijn of haar werkschema",
    "tutornotavailablehasevent" => "docent niet beschikbaar vanwege een bestaande lesafspraak",
    "tutors" => "docent|docenten",
    "tutorsmissing" => "geen docenten gevonden",
    "twofactorauthenticate" => "bevestig",
    "twofactorauthentication" => "2 factor beveiliging",
    "twofactorauthenticatorcode" => "Beveiligingscode",
    "twofactorconfirm" => "bevestig activeren 2 factor beveiliging",
    "twofactordisable" => "2 factor beveiliging uitzetten",
    "twofactordisableexplain" => "Als je als de 2 factor beveiliging wilt <strong>deactiveren</strong>: voer je wachtwoord in en klik op [2 factor beveiliging uitzetten].",
    "twofactorenabledsuccess" => "2 factor beveiliging is succesvol geactiveerd.",
    "twofactorexplain" => "2 factor beveiliging (2FA) versterkt de toegangsbeveiliging door op twee manieren (factoren) je identiteit te bevestigen: je wachtwoord en een code die op het moment van inloggen wordt gegenereerd. 2 factor beveiliging beschermt tegen phishing, social engineering en password brute force attacks en beveiligt je login formulier tegen aanvallers die proberen gebruik te maken van gestolen inloggegevens.",
    "twofactorgeneratekey" => "genereer een beveiligingssleutel om 2 factor beveiliging te activeren",
    "twofactorinvalidcode" => "Ongeldige verificatiecode.",
    "twofactorisenabled" => "2 factor beveiliging is <strong>actief</strong> op je account.",
    "twofactorismissing" => "2 factor beveiliging voor inloggen is nog niet actief.",
    "twofactornowdisabled" => "2 factor beveiliging is nu gedeactiveerd",
    "twofactorsecretkeygenerated" => "Je code is gegenereerd.",
    "twofactorstep1" => "Scan deze QR code met je authenticator app. Je kunt ook deze code gebruiken:",
    "twofactorstep2" => "Voer de verificatiecode in die je van de authenticator app krijgt:",
    "twoweeks" => "twee weken",
    "type" => "type",
    "typeDesc" => "type / omschrijving",
    "typetosearch" => "type om te zoeken",
    "unabletodeletecourse" => "verwijderen van de cursus is mislukt",
    "unabletodeletetutor" => "verwijderen van de docent is mislukt",
    "uncheckeditems" => "niet aangevinkte items",
    "uniquestudentidentifier" => "unieke leerlingcode",
    "unknown" => "onbekend",
    "unlink" => "ontkoppel",
    "unreadmessages" => "ongelezen berichten",
    "unregister" => "uitschrijven",
    "unregisterdate" => "uitschrijfdatum",
    "unregistered" => "uitgeschreven",
    "unsetsticky" => "klik om los te maken: op dit moment komt deze gebeurtenis in de kalender, hoewel er tegelijkertijd andere blokkerende gebeurtenissen zijn",
    "until" => "tot",
    "until_unregister_indefinitely" => "tot uitschrijven (doorlopend)",
    "untilincluding" => "tot en met",
    "update" => "bijwerken",
    "updated" => "bijgewerkt",
    "updatefailed" => "bijwerken mislukt",
    "updatefile" => "upload een nieuwe versie",
    "updatemydatainclass" => "update mijn leerlinggegevens in Class",
    "updatesuccess" => "bijwerken gelukt",
    "upload" => "upload",
    "uploaddocandlink" => "upload een document en koppel het aan deze bibliotheek",
    "uploadICS" => "upload ICS",
    "uploadimagesyouwillbeusing" => "Upload hier de afbeeldingen die je wilt gebruiken",
    "uploading" => "uploading",
    "uploadingandsaving" => "uploaden en opslaan....",
    "uploadnewversionoffile" => "upload een nieuwe versie van dit bestand in -alle- bibliotheken",
    "url" => "URL",
    "use&&tosearchformoreargs" => "gebruik && om op meer dan 1 argument te zoeken",
    "usefor" => "gebruik voor",
    "useforfieldupdated" => "&quot;gebruik-voor&quot; veld bijgewerkt",
    "useforfinance" => "financieel",
    "useforplanning" => "planning",
    "useforpromotions" => "promoties",
    "usesalutation" => "alternatieve aanhef",
    "validating" => "controleren op conflicten",
    "value" => "waarde",
    "variables" => "variabele|variabelen",
    "variant" => "variant",
    "variantcode" => "variant code",
    "vatliable" => "BTW plichtig",
    "view" => "bekijk",
    "viewavatar" => "Bekijk avatar",
    "viewsignrequest" => "bekijk ondertekenverzoek",
    "viewstudentspreferencepage" => "ga naar de voorkeurpagina van deze leerling",
    "wantstokeepdatetime" => "wil deze datum/tijd behouden",
    "warn" => "waarschuwing",
    "warnbeforeadultstudents" => "waarschuw leerling BTW plichtig (dagen)",
    "warnbeforebirthdaystudents" => "waarschuw verjaardag leerlingen (dagen)",
    "warndeletetoken" => "Weet je zeker dat je dit token wilt verwijderen? Dit kan de toegang van je website of andere afnemers tot informatie uit CLASS blokkeren!",
    "warnsendmailtoselffirst" => "Advies: stuur een mailing eerst naar jezelf om de opmaak en inhoud te testen!",
    "warnunsavedchanges" => "Als je deze pagina verlaat, verlies je niet-opgeslagen wijzigingen.",
    "webdescription" => "webbeschrijving (Wordpress)",
    "weburlofyourprofilephoto" => "web url van je profielfoto",
    "week" => "week",
    "weeks" => "week|weken",
    "welcome" => "welkom",
    "wewillbedeleting" => "dit gaan we verwijderen",
    "whichplanningasstartingpoint" => "welke planning vormt de basis voor deze plansessie?",
    "whichplanningastarget" => "in welke planning wil je de nieuwe afspraken opnemen",
    "whichregistrationstoplan" => "welke inschrijvingen wil je gaan inplannen?",
    "who" => "wie",
    "wholeschool" => "hele school",
    "willbe" => "wordt",
    "with" => "met",
    "withoutplanningpreferrence" => "zonder planningvoorkeur",
    "withoutpreviousplanning" => "zonder voorgaande planning",
    "withplanningpreferrence" => "met planningvoorkeur",
    "withpreviousplanning" => "met voorgaande planning",
    "wordpresscode" => "WordPress code",
    "working" => "Bezig",
    "wouldyouliketocreategroup" => "Wil je een leerlinggroep aanmaken?",
    "wrongtargetcourse" => "Deze leerling is op een andere cursus ingeschreven dan waar de groep voor is",
    "year" => "jaar",
    "years" => "jaar|jaren",
    "yes" => "ja",
    "yessend" => "ja, verstuur",
    "youareabouttodeletetimeslice" => "je staat op het punt dit tijdsegment te verwijderen",
    "youareusingdefaultpw" => "je gebruikt het standaard wachtwoord. Dit moet eerst gewijzigd worden, voor je toegang krijgt tot Class.",
    "youcanaddrecurrenceoptionshere" => "je kunt <a href=\"/recurrenceoptions\">hier</a> herhaalopties toevoegen",
    "youcanfindthemhere" => "je vindt ze hier: <a href=':tanccUrl' target='_blank'>:tanccUrl</a>",
    "youcannowclosethiswindow" => "je kunt nu dit venster sluiten",
    "youcantthisbecauseisdateexception" => "je kunt dit niet hier veranderen omdat het een datumuitzondering is.",
    "youcanusetheseplaceholders" => "Je kunt in de tekst de ankers gebruiken die in de het menu onder [ankers] staan. In de uiteindelijke e-mail worden ze automatisch vervangen.",
    "youhavenocourses" => "je hebt (nog) geen actuele inschrijving in een cursus",
    "youhavenotcreatedtokens" => "je hebt (nog) geen toegangscode gemaakt",
    "youhavesuccessfullyuploadedimages" => "Je hebt succesvol een afbeelding ge&uuml;pload|Je hebt succesvol :nrofimages afbeeldingen ge&uuml;pload",
    "youneedthisidtodelete" => "je hebt dit nummer nodig om alle gegevens van deze leerling te verwijderen (zie je profiel pagina)",
    "yourappointmentwith" => "je afspraak bij :customername",
    "youravailability" => "wanneer ben je beschikbaar?",
    "yourfirstupcomingappointment" => "Je eerst volgende afspraak",
    "yournewpassword" => "je nieuwe wachtwoord",
    "yournewpasswordis" => "je nieuwe wachtwoord is",
    "yournewpasswordneedsto" => "status wachtwoordwijziging",
    "yourregistrationisnowconfirmed" => "je inschrijving is bevestigd",
    "zipcode" => "postcode"
];
