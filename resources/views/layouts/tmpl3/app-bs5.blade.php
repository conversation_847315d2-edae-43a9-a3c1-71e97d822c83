<!DOCTYPE html>
<html lang="{{ App::getLocale() }}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    {{-- CSRF Token --}}
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <meta name="description" content="program to help small schools to administrate students, courses, tutors and their planning" />
    <meta name="author" content="Scolavisa, www.scolavisa.eu" />

    <title>{{ $pageTitle ?? 'CLASS4' }}</title>
    <link rel="icon" type="image/x-icon" href="{{ url('/favicon-4.ico') }}?v={{ time() }}" />

    {{-- Bootstrap 5 from CDN (replacing Bootstrap 4) --}}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
          crossorigin="anonymous">

    {{-- DataTables Bootstrap 5 styling --}}
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet" crossorigin="anonymous" />

    {{-- Custom Bootstrap 5 styles for Class application --}}
    <link href="{{ url('/css/bootstrap5-custom.css') }}" rel="stylesheet" />

    {{-- Bootstrap 5 layout adjustments to match Bootstrap 4 --}}
    <link href="{{ url('/css/bootstrap5-layout-adjustments.css') }}?v={{ time() }}" rel="stylesheet" />

    {{-- Font Awesome --}}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/js/all.min.js" crossorigin="anonymous"></script>

    {{-- CK Editor styles --}}
    <link href="{{ url('/css/ckeditor.css') }}" rel="stylesheet" />

    {{-- Scripts --}}
    <script>
        window.Laravel = <?php echo json_encode([
            'csrfToken' => csrf_token(),
        ]); ?>;
    </script>

    <script>
        window.trans = <?php
        // copy all translations from /resources/lang/CURRENT_LOCALE/* to global JS variable
        // $lang_files = File::files(resource_path() . '/lang/' . App::getLocale() . '/generic.php');
        $lang_files = [
            resource_path() . '/lang/' . App::getLocale() . '/email.php',
            resource_path() . '/lang/' . App::getLocale() . '/generic.php',
            resource_path() . '/lang/' . App::getLocale() . '/localisation.php',
            resource_path() . '/lang/' . App::getLocale() . '/mailtargets.php',
            resource_path() . '/lang/' . App::getLocale() . '/priceinterval.php',
            resource_path() . '/lang/' . App::getLocale() . '/reporting.php',
            resource_path() . '/lang/' . App::getLocale() . '/templates.php',
            resource_path() . '/lang/' . App::getLocale() . '/pagination.php',
        ];
        $trans = [];
        foreach ($lang_files as $f) {
            $filename = pathinfo($f)['filename'];
            $trans[$filename] = trans($filename);
        }
        echo json_encode($trans);
        ?>;
        window.logedinuserid = '<?php
            $user = \Illuminate\Support\Facades\Auth::user();
            echo isset($user) ? $user->id : 0
            ?>';
    </script>

    @yield("extra_style")

    {{-- no bugsnag during development --}}
    @if(strtolower(config('app.env')) !== 'development' && strtolower(config('app.env')) !== 'local')
        {{-- https://app.bugsnag.com --}}
        <script src="//d2wy8f7a9ursnm.cloudfront.net/bugsnag-3.min.js"
                data-apikey="3fbaaa870d7340cf45f104d0cee2f656"
                data-releasestage="{{config('app.env')}}"></script>
    @endif

</head>
<body class="sb-nav-fixed">
<div id="vuecontext">
    @include('layouts.tmpl3._navbar-bs5')

    <div id="layoutSidenav">

        <div id="layoutSidenav_content">
            <main>
                <div class="container-fluid pt-sm-2 pt-lg-5">
{{--                     @include('layouts.tmpl3._showbreakpoint')--}}
                    @yield('content')
                </div>
            </main>
            @include('layouts.tmpl3._footer-bs5')
        </div>
    </div>
</div>

{{-- Bootstrap 5 JavaScript (replacing Bootstrap 4) --}}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
        crossorigin="anonymous"></script>

{{-- Main application JavaScript and CSS via Vite --}}
@vite(['resources/js/class3/app.js', 'resources/sass/app.scss'])

{{-- Initialize Bootstrap 5 components --}}
<script>
    // Initialize Bootstrap 5 components when the DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize navbar toggler using Bootstrap 5 Collapse API
        const navbarToggler = document.querySelector('.navbar-toggler');
        if (navbarToggler) {
            const targetId = navbarToggler.getAttribute('data-bs-target');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                // Create a new Collapse instance
                const bsCollapse = new bootstrap.Collapse(targetElement, {
                    toggle: false // Don't toggle on initialization
                });

                // Add click event listener to toggle the collapse
                navbarToggler.addEventListener('click', function() {
                    bsCollapse.toggle();
                });
            }
        }
    });
</script>

@yield('scripts')
</body>
</html>
