// Simplified global styling for Class application
// Bootstrap 5 is loaded via CDN, so we only include custom styles

html,
body {
  height: 100%;
}

/** Set spacing on containers for page symmetry */
.container,
.container-fluid {
  padding-left: 1.5rem; /* Bootstrap 5 default gutter width */
  padding-right: 1.5rem; /* Bootstrap 5 default gutter width */
}

.bg-blue {
  background-color: $classblue;
}

/** positioning and size of class logo top left */

.class4logo {
  position: absolute;
  height: 3.5rem;
  left: 0;
  top: 0;
}

/** menu icons all the same span so the text starts at the same point */
.sb-nav-link-icon {
  width: 1.5em;
  position: relative;
}

/** Small alert (less height) */
.alert-sm {
  margin-bottom: 1px;
  height: 30px;
  line-height:30px;
  padding: 0.2rem 1rem !important;
}

/** Default tag styles */
a {
  color: $classred;
  &:hover {
    color: $classdarkblue;
  }
}

/** layout mobile menu */
.navbar-collapse {
  background-color: $classblue !important;
  @media (max-width: 87.4375em) { // large breakpoint
    border-radius: 5px;
    margin-bottom: 3rem;
    .nav-item {
      margin-top: 3rem;
      margin-left: 1.5rem;
    }
    .dropdown-item, .dropdown-item-nc {
      padding: 1rem 1.5rem;
    }
    #dropdownMenuButtonProfile {
      margin-top: 0.8rem;
    }
  }
}

/** Create a dropdown item non-clickable. To be used -instead of- .dropdown-item */
.dropdown-item-nc {
    display: block;
    width: 100%;
    padding: 0.25rem 1.5rem;
    clear: both;
    font-weight: 400;
    color: $classlight;
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    cursor: default;
}

// checkbox active
*[type="checkbox"].regular-checkbox {
  -webkit-appearance: none;
  font-size: 1rem;
  padding: .2rem;
  border: solid $classred 1px;
  border-radius: 3px;
  display: inline-block;
  top: 3px;
  width: 2rem;
  height: 2rem;
  &:active, &:checked:active {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  &:checked {
    background-color: #e9ecee;
    color: $classred;
    font-size: 1rem;
    padding: .2rem;
    &:after {
      content: '\2714';
      position: relative;
      top: 0;
      left: 3px;
      color: $classred;
      border: none;
    }
  }
  &:focus {
    outline: 0;
  }
}

// temp solution: if a remark present for this event, add a flag so the user's attention is drawn
// see sc-2817
.vuecal__event:has(div.hasRemarks) {
  border-right: solid 20px $classred;
}
