import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/js/class3/app.js'
            ],
            refresh: true,
            // Configure output to match current Mix structure
            buildDirectory: 'build',
        }),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
    ],
    resolve: {
        alias: {
            vue: 'vue/dist/vue.esm-bundler.js',
        },
        extensions: ['.ts', '.vue', '.js'],
    },
    build: {
        // Configure output to match current structure
        outDir: 'public',
        assetsDir: '',
        rollupOptions: {
            output: {
                entryFileNames: 'js/tmpl3/[name].js',
                chunkFileNames: 'js/tmpl3/[name].js',
                assetFileNames: (assetInfo) => {
                    if (assetInfo.name && assetInfo.name.endsWith('.css')) {
                        return 'css/tmpl3/[name][extname]';
                    }
                    return 'assets/[name][extname]';
                }
            }
        }
    },
    server: {
        host: '0.0.0.0',
        port: 5173,
        strictPort: true,
        hmr: {
            host: 'localhost',
            port: 5173,
        },
    },
});
