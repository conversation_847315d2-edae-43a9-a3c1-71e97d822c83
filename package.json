{"name": "class", "version": "3.0.0", "description": "<PERSON> <PERSON>vel Admin <PERSON>", "keywords": ["laravel", "class", "tutors", "courses", "schedules", "students", "teaching"], "license": "MIT", "private": true, "type": "module", "scripts": {"generate-components": "node scripts/generate-component-imports.cjs", "development": "npm run generate-components && vite build", "production": "npm run generate-components && vite build", "watch": "npm run generate-components && vite", "hot": "npm run generate-components && vite", "dev": "npm run development", "prod": "npm run production", "test": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest watch", "lint": "eslint resources/js/class3 --ext .js,.vue", "lint:fix": "eslint resources/js/class3 --ext .js,.vue --fix", "cypress:open": "cypress open", "cypress:run": "cypress run --browser chrome", "cr": "npm run cypress:run"}, "devDependencies": {"@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@types/node": "^20.14.9", "@vitejs/plugin-vue": "^5.2.3", "@vitest/coverage-v8": "^1.4.0", "@vue/compiler-sfc": "^3.3.0", "@vue/test-utils": "^2.4.0", "babel-preset-env": "^1.7.0", "c8": "^10.1.3", "cross-env": "^5.2.1", "eslint": "^8.57.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.22.0", "flush-promises": "^1.0.2", "jsdom": "^24.0.0", "laravel-vite-plugin": "^1.3.0", "lodash": "^4.17.20", "moment-timezone": "^0.5.47", "sass": "1.69.5", "vite": "^5.4.19", "vitest": "^1.4.0", "vue": "^3.3.0", "vue-loader": "^17.0.0"}, "dependencies": {"@ckeditor/ckeditor5-vue": "^7.1.0", "@fortawesome/fontawesome-free": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.3", "@fullcalendar/core": "^6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.10", "@fullcalendar/list": "^6.1.10", "@fullcalendar/timegrid": "^6.1.10", "@fullcalendar/vue3": "^6.1.10", "@hennge/vue3-pagination": "^1.0.17", "@vueform/multiselect": "^2.6.6", "@vuepic/vue-datepicker": "^11.0.2", "@vueup/vue-quill": "^1.2.0", "ajv": "^6.12.6", "axios": "^1.9.0", "chart.js": "^4.4.9", "ckeditor5": "^45.1.0", "clipboard": "^1.7.1", "codemirror": "^5.65.19", "color": "^4.2.3", "css-color-names": "^1.0.1", "cypress": "^13.16.0", "del": "^8.0.0", "file-loader": "^6.2.0", "floating-vue": "^2.0.0-beta.24", "imagemin": "^8.0.1", "jdenticon": "^3.3.0", "mitt": "^3.0.0", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "otplib": "^12.0.1", "postcss-loader": "^5.2.0", "scroll-into-view": "^1.16.1", "vue-cal": "^4.10.2", "vue-datepicker-next": "^1.0.3", "vue-draggable-next": "^2.2.1", "vue-window-size": "^1.0.0"}, "dependenciesComments": {"sass": "use 1.32.13 to suppress warnings for bootstrap using / instead of math.div, until bs fixes this", "bootstrap-sass": "3.3.7 the same math.div warning", "remark": "Migrating to Vue 3 - replaced Vue 2 packages with Vue 3 compatible alternatives", "remark-summernote": "Migrating to Vue 3 removed \"summernote\": \"^0.9.1\",", "remark-element-plus": "Migrating to Vue 3 removed \"element-ui\": \"^2.5.6\",", "remark--tip-tap": "Migrating to Vue 3 removed \"@tiptap/extension-image\": \"^2.1.13\",\"@tiptap/starter-kit\": \"^2.1.13\",\"@tiptap/vue-3\": \"^2.1.13\",", "remark-playwright": "Migrating to Vue 3 removed \"@playwright/test\": \"^1.45.1\",\n"}, "web-types": "./web-types.json"}