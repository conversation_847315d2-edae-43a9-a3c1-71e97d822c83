/* Class Application Styles - Modern CSS Version */
/* Using CSS custom properties instead of SASS variables */

/* Base Layout */
html,
body {
  height: 100%;
}

/* Container spacing */
.container,
.container-fluid {
  padding-left: 1.5rem; /* Bootstrap 5 default gutter width */
  padding-right: 1.5rem; /* Bootstrap 5 default gutter width */
}

/* Brand Colors */
.bg-blue {
  background-color: var(--class-blue);
}

/* Logo positioning */
.class3logo {
  position: absolute;
  width: 14.0625rem;
  height: 100%;
  left: 0;
  top: 0;
}

/* Navigation icons */
.sb-nav-link-icon {
  width: 1.5em;
  position: relative;
}

/* Small alerts */
.alert-sm {
  margin-bottom: 1px;
  height: 30px;
  line-height: 30px;
  padding: 0.2rem 1rem !important;
}

/* Link colors */
a {
  color: var(--class-red);
}

a:hover {
  color: var(--class-dark-blue);
}

/* Mobile navigation */
.navbar-collapse {
  background-color: var(--class-blue) !important;
}

@media (max-width: 87.4375em) { /* large breakpoint */
  .navbar-collapse {
    border-radius: 5px;
    margin-bottom: 3rem;
  }
  
  .navbar-collapse .nav-item {
    margin-top: 3rem;
    margin-left: 1.5rem;
  }
  
  .navbar-collapse .dropdown-item,
  .navbar-collapse .dropdown-item-nc {
    padding: 1rem 1.5rem;
  }
  
  .navbar-collapse #dropdownMenuButtonProfile {
    margin-top: 0.8rem;
  }
}

/* Non-clickable dropdown item */
.dropdown-item-nc {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: var(--class-light);
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  cursor: default;
}

/* Custom checkbox styling */
*[type="checkbox"].regular-checkbox {
  -webkit-appearance: none;
  font-size: 1rem;
  padding: 0.2rem;
  border: solid var(--class-red) 1px;
  border-radius: 3px;
  display: inline-block;
  top: 3px;
  width: 2rem;
  height: 2rem;
}

*[type="checkbox"].regular-checkbox:active,
*[type="checkbox"].regular-checkbox:checked:active {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

*[type="checkbox"].regular-checkbox:checked {
  background-color: #e9ecee;
  color: var(--class-red);
  font-size: 1rem;
  padding: 0.2rem;
}

*[type="checkbox"].regular-checkbox:checked:after {
  content: '\2714';
  position: relative;
  top: 0;
  left: 3px;
  color: var(--class-red);
  border: none;
}

*[type="checkbox"].regular-checkbox:focus {
  outline: 0;
}

/* Vue Calendar event styling */
.vuecal__event:has(div.hasRemarks) {
  border-right: solid 20px var(--class-red);
}
